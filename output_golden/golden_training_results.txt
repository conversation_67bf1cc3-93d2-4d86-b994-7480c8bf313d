MVGNN-GTE Golden Features Training Results Summary
Fusion of Successful MVGNN Design with 1061-dim Golden Features
============================================================
Training Configuration:
{'node_features': 1061, 'use_golden_features': True, 'debug_mode': False, 'edge_features': 16, 'hidden_dim': 64, 'num_encoder_layers': 2, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 3, 'patience': 15, 'learning_rate': 0.001, 'dropout': 0.3, 'use_unet_gt': False, 'pooling_ratio': 0.3, 'use_coords_update': False, 'use_gcn_fusion': True, 'use_stable_scheduler': True, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.25, 'focal_gamma': 2.0, 'class_weight_ratio': 1.0}

Cross-Validation Results:
Fold 1: AUC = 0.551897
Fold 2: AUC = 0.594456

Final Results:
Mean AUC: 0.573176 ± 0.021279
Best AUC: 0.594456
Training completed at: 2025-07-30 21:45:47
