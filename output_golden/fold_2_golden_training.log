MVGNN-GTE Golden Features Training Configuration:
{'node_features': 1061, 'use_golden_features': True, 'debug_mode': False, 'edge_features': 16, 'hidden_dim': 128, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 8, 'epochs': 30, 'patience': 15, 'learning_rate': 0.001, 'dropout': 0.3, 'use_unet_gt': False, 'pooling_ratio': 0.3, 'use_coords_update': False, 'use_gcn_fusion': True, 'use_stable_scheduler': True, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.25, 'focal_gamma': 2.0, 'class_weight_ratio': 1.0}

Epoch 1/30 - Train Loss: 0.458778, Val Loss: 0.431576, Train AUC: 0.494908, Val AUC: 0.427501, Train F1: 0.010346, Val F1: 0.000000, Train Precision: 0.129412, Val Precision: 0.000000, Train Recall: 0.005388, Val Recall: 0.000000
Epoch 1/30 - Train Loss: 0.458778, Val Loss: 0.431576, Train AUC: 0.494908, Val AUC: 0.427501, Train F1: 0.010346, Val F1: 0.000000, Train Precision: 0.129412, Val Precision: 0.000000, Train Recall: 0.005388, Val Recall: 0.000000
🎯 Model saved at epoch 1 with validation AUC: 0.427501
Epoch 2/30 - Train Loss: 0.443847, Val Loss: 0.431475, Train AUC: 0.487960, Val AUC: 0.438285, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 2/30 - Train Loss: 0.443847, Val Loss: 0.431475, Train AUC: 0.487960, Val AUC: 0.438285, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 2 with validation AUC: 0.438285
Epoch 3/30 - Train Loss: 0.450904, Val Loss: 0.432911, Train AUC: 0.507774, Val AUC: 0.500539, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 3/30 - Train Loss: 0.450904, Val Loss: 0.432911, Train AUC: 0.507774, Val AUC: 0.500539, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 3 with validation AUC: 0.500539
Epoch 4/30 - Train Loss: 0.443801, Val Loss: 0.436331, Train AUC: 0.512495, Val AUC: 0.412417, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 5/30 - Train Loss: 0.449732, Val Loss: 0.432811, Train AUC: 0.488570, Val AUC: 0.440920, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 6/30 - Train Loss: 0.450736, Val Loss: 0.430251, Train AUC: 0.489464, Val AUC: 0.505614, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 6/30 - Train Loss: 0.450736, Val Loss: 0.430251, Train AUC: 0.489464, Val AUC: 0.505614, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 6 with validation AUC: 0.505614
Epoch 7/30 - Train Loss: 0.444460, Val Loss: 0.433574, Train AUC: 0.493508, Val AUC: 0.525774, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 7/30 - Train Loss: 0.444460, Val Loss: 0.433574, Train AUC: 0.493508, Val AUC: 0.525774, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 7 with validation AUC: 0.525774
Epoch 8/30 - Train Loss: 0.440474, Val Loss: 0.429678, Train AUC: 0.499710, Val AUC: 0.506247, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 9/30 - Train Loss: 0.442926, Val Loss: 0.437005, Train AUC: 0.493253, Val AUC: 0.521067, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 10/30 - Train Loss: 0.445714, Val Loss: 0.433372, Train AUC: 0.489311, Val AUC: 0.465324, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 11/30 - Train Loss: 0.446749, Val Loss: 0.431029, Train AUC: 0.508393, Val AUC: 0.618760, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 11/30 - Train Loss: 0.446749, Val Loss: 0.431029, Train AUC: 0.508393, Val AUC: 0.618760, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 11 with validation AUC: 0.618760
Epoch 12/30 - Train Loss: 0.447667, Val Loss: 0.434865, Train AUC: 0.498199, Val AUC: 0.444326, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 13/30 - Train Loss: 0.446329, Val Loss: 0.431158, Train AUC: 0.509837, Val AUC: 0.485533, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 14/30 - Train Loss: 0.447346, Val Loss: 0.431950, Train AUC: 0.512992, Val AUC: 0.575945, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 15/30 - Train Loss: 0.444943, Val Loss: 0.434426, Train AUC: 0.533879, Val AUC: 0.425863, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 16/30 - Train Loss: 0.443120, Val Loss: 0.430176, Train AUC: 0.506348, Val AUC: 0.586148, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 17/30 - Train Loss: 0.443815, Val Loss: 0.431639, Train AUC: 0.516225, Val AUC: 0.597848, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 18/30 - Train Loss: 0.447429, Val Loss: 0.430052, Train AUC: 0.495183, Val AUC: 0.585503, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 19/30 - Train Loss: 0.444697, Val Loss: 0.430125, Train AUC: 0.493345, Val AUC: 0.595333, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 20/30 - Train Loss: 0.449933, Val Loss: 0.430670, Train AUC: 0.523777, Val AUC: 0.621602, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 20/30 - Train Loss: 0.449933, Val Loss: 0.430670, Train AUC: 0.523777, Val AUC: 0.621602, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 20 with validation AUC: 0.621602
Epoch 21/30 - Train Loss: 0.442572, Val Loss: 0.431095, Train AUC: 0.507256, Val AUC: 0.621598, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 22/30 - Train Loss: 0.442091, Val Loss: 0.431172, Train AUC: 0.506315, Val AUC: 0.610082, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 23/30 - Train Loss: 0.447542, Val Loss: 0.438019, Train AUC: 0.506221, Val AUC: 0.617784, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 24/30 - Train Loss: 0.443161, Val Loss: 0.429591, Train AUC: 0.505985, Val AUC: 0.623786, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 24/30 - Train Loss: 0.443161, Val Loss: 0.429591, Train AUC: 0.505985, Val AUC: 0.623786, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
🎯 Model saved at epoch 24 with validation AUC: 0.623786
Epoch 25/30 - Train Loss: 0.442366, Val Loss: 0.430236, Train AUC: 0.556885, Val AUC: 0.587618, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 26/30 - Train Loss: 0.443101, Val Loss: 0.432877, Train AUC: 0.513457, Val AUC: 0.589881, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 27/30 - Train Loss: 0.447223, Val Loss: 0.429779, Train AUC: 0.483097, Val AUC: 0.538010, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 28/30 - Train Loss: 0.447511, Val Loss: 0.430415, Train AUC: 0.498539, Val AUC: 0.602720, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 29/30 - Train Loss: 0.444154, Val Loss: 0.429517, Train AUC: 0.481796, Val AUC: 0.539983, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000
Epoch 30/30 - Train Loss: 0.442235, Val Loss: 0.431262, Train AUC: 0.513786, Val AUC: 0.590700, Train F1: 0.000000, Val F1: 0.000000, Train Precision: 0.000000, Val Precision: 0.000000, Train Recall: 0.000000, Val Recall: 0.000000

✅ Fold 2 completed with best AUC: 0.623786
============================================================

