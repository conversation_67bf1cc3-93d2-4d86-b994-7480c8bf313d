MVGNN-GTE Golden Features Training Configuration:
{'node_features': 1061, 'use_golden_features': True, 'edge_features': 16, 'hidden_dim': 128, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 8, 'epochs': 50, 'patience': 8, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': False, 'pooling_ratio': 0.3, 'use_coords_update': Fals<PERSON>, 'use_gcn_fusion': True, 'use_stable_scheduler': True, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.25, 'focal_gamma': 2.0, 'class_weight_ratio': 1.0}

