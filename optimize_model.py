#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 模型优化脚本 - 简化版
基于当前AUC 0.675的基线，系统性优化关键参数
"""

import torch
import pandas as pd
import numpy as np
from sklearn.model_selection import KFold
from sklearn.metrics import roc_auc_score
import os
import argparse
from tqdm import tqdm
import json
from datetime import datetime

# 导入模型和工具
from model_gte import MVGNN_GTE
from utils import TaskDataset
from focalLoss import Focal<PERSON>oss

def load_protein_data(feature_path):
    """加载蛋白质特征数据"""
    df = pd.read_csv('./datasets/PRO_Train335.csv')
    protein_data = {}
    
    for pdb_id in df['ID'].unique():
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f'{pdb_id}_X.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_node_feature.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_mask.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_label.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_adj.tensor', weights_only=True)
            )
        except FileNotFoundError:
            continue
    
    print(f"✅ 加载了 {len(protein_data)} 个蛋白质特征")
    return protein_data, df

def train_and_evaluate(config, train_df, val_df, protein_data, device):
    """训练并评估单个配置"""
    # 创建数据集
    train_dataset = TaskDataset(
        train_df, protein_data, 'label',
        use_graph_format=True,
        use_geometric_features=True,
        training=True,
        use_golden_features=False
    )
    
    val_dataset = TaskDataset(
        val_df, protein_data, 'label',
        use_graph_format=True,
        use_geometric_features=True,
        training=False,
        use_golden_features=False
    )
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=config['batch_size'],
        shuffle=True,
        collate_fn=train_dataset.collate_fn
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        collate_fn=val_dataset.collate_fn
    )
    
    # 创建模型
    model = MVGNN_GTE(
        node_features=config['node_features'],
        edge_features=config['edge_features'],
        hidden_dim=config['hidden_dim'],
        num_encoder_layers=config['num_encoder_layers'],
        k_neighbors=config['k_neighbors'],
        augment_eps=0.0,
        dropout=config['dropout'],
        num_heads=4,
        egnn_layers=1,
        use_coords_update=config['use_coords_update'],
        use_unet_gt=config['use_unet_gt'],
        pooling_ratio=config['pooling_ratio'],
        use_geometric_features=True,
        num_iterations=1,
        use_global_node=config['use_global_node']
    ).to(device)
    
    # 创建损失函数
    pos_weight = torch.tensor([config['class_weight_ratio']]).to(device)
    bce_loss = torch.nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    focal_loss = FocalLoss(alpha=config['focal_alpha'], gamma=config['focal_gamma'])
    
    def combined_loss(outputs, targets):
        """组合损失函数"""
        focal = focal_loss(outputs, targets)
        bce = bce_loss(outputs, targets)
        return config['focal_weight'] * focal + (1 - config['focal_weight']) * bce
    
    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 训练循环
    best_val_auc = 0.0
    patience_counter = 0
    
    for epoch in range(config['epochs']):
        # 训练
        model.train()
        train_loss = 0.0
        
        for batch in train_loader:
            try:
                # 将数据移动到设备
                for key in batch:
                    if isinstance(batch[key], torch.Tensor):
                        batch[key] = batch[key].to(device)
                
                # 前向传播
                optimizer.zero_grad()
                outputs = model(batch)
                loss = combined_loss(outputs, batch['labels'])
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
            except Exception as e:
                print(f"训练批次错误: {e}")
                continue
        
        # 验证
        if (epoch + 1) % 2 == 0 or epoch == config['epochs'] - 1:
            model.eval()
            val_predictions = []
            val_labels = []
            
            with torch.no_grad():
                for batch in val_loader:
                    try:
                        # 将数据移动到设备
                        for key in batch:
                            if isinstance(batch[key], torch.Tensor):
                                batch[key] = batch[key].to(device)
                        
                        # 前向传播
                        outputs = model(batch)
                        predictions = torch.sigmoid(outputs).cpu().numpy()
                        labels = batch['labels'].cpu().numpy()
                        
                        val_predictions.extend(predictions)
                        val_labels.extend(labels)
                    except Exception as e:
                        print(f"验证批次错误: {e}")
                        continue
            
            # 计算AUC
            if len(val_predictions) > 0 and len(set(val_labels)) > 1:
                val_auc = roc_auc_score(val_labels, val_predictions)
                print(f"Epoch {epoch+1}/{config['epochs']}, Val AUC: {val_auc:.4f}")
                
                if val_auc > best_val_auc:
                    best_val_auc = val_auc
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                if patience_counter >= config['patience']:
                    print(f"早停: {patience_counter} 个epoch没有改善")
                    break
    
    return best_val_auc

def main():
    parser = argparse.ArgumentParser(description='模型优化')
    parser.add_argument('--output_dir', default='./model_optimization', help='输出目录')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    protein_data, df = load_protein_data('./feature/')
    
    # 创建交叉验证分割
    kfold = KFold(n_splits=5, shuffle=True, random_state=42)
    fold_splits = list(kfold.split(df))
    
    # 基础配置
    base_config = {
        'node_features': 1297,  # ESM2特征 + 几何特征
        'edge_features': 16,
        'hidden_dim': 96,
        'num_encoder_layers': 4,
        'k_neighbors': 30,
        'batch_size': 4,
        'epochs': 15,
        'patience': 5,
        'learning_rate': 0.0002,
        'weight_decay': 1e-5,
        'dropout': 0.3,
        'use_unet_gt': True,
        'pooling_ratio': 0.3,
        'use_coords_update': False,
        'use_global_node': True,
        'focal_alpha': 0.75,
        'focal_gamma': 3.0,
        'focal_weight': 0.7,  # focal_loss的权重
        'class_weight_ratio': 5.4
    }
    
    # 定义要测试的配置
    configs = [
        # 基础配置
        base_config.copy(),
        
        # 学习率变化
        {**base_config, 'learning_rate': 0.0005},
        {**base_config, 'learning_rate': 0.001},
        
        # 隐藏层维度变化
        {**base_config, 'hidden_dim': 128},
        {**base_config, 'hidden_dim': 160},
        
        # Dropout变化
        {**base_config, 'dropout': 0.2},
        {**base_config, 'dropout': 0.4},
        
        # 编码器层数变化
        {**base_config, 'num_encoder_layers': 3},
        {**base_config, 'num_encoder_layers': 5},
        
        # Focal Loss参数变化
        {**base_config, 'focal_alpha': 0.5, 'focal_gamma': 2.0},
        {**base_config, 'focal_alpha': 0.25, 'focal_gamma': 4.0},
        
        # 损失函数权重变化
        {**base_config, 'focal_weight': 0.5},
        {**base_config, 'focal_weight': 0.9},
        
        # 类别权重变化
        {**base_config, 'class_weight_ratio': 3.0},
        {**base_config, 'class_weight_ratio': 8.0},
    ]
    
    # 评估所有配置
    results = []
    best_auc = 0.0
    best_config = None
    
    for i, config in enumerate(configs):
        print(f"\n{'='*60}")
        print(f"🔧 配置 {i+1}/{len(configs)}")
        print(f"学习率: {config['learning_rate']}")
        print(f"隐藏层维度: {config['hidden_dim']}")
        print(f"Dropout: {config['dropout']}")
        print(f"编码器层数: {config['num_encoder_layers']}")
        print(f"Focal参数: alpha={config['focal_alpha']}, gamma={config['focal_gamma']}")
        print(f"损失函数权重: focal={config['focal_weight']}, bce={1-config['focal_weight']}")
        print(f"类别权重: {config['class_weight_ratio']}")
        
        # 只使用前两折进行快速评估
        fold_aucs = []
        for fold_idx, (train_idx, val_idx) in enumerate(fold_splits[:2]):
            print(f"\n第 {fold_idx+1} 折评估:")
            train_df_fold = df.iloc[train_idx].reset_index(drop=True)
            val_df_fold = df.iloc[val_idx].reset_index(drop=True)

            # 过滤数据
            train_df_fold = train_df_fold[train_df_fold['ID'].isin(protein_data.keys())].reset_index(drop=True)
            val_df_fold = val_df_fold[val_df_fold['ID'].isin(protein_data.keys())].reset_index(drop=True)
            
            # 训练和评估
            fold_auc = train_and_evaluate(config, train_df_fold, val_df_fold, protein_data, device)
            fold_aucs.append(fold_auc)
            print(f"第 {fold_idx+1} 折 AUC: {fold_auc:.4f}")
        
        # 计算平均AUC
        mean_auc = np.mean(fold_aucs)
        print(f"\n配置 {i+1} 平均AUC: {mean_auc:.4f}")
        
        # 保存结果
        result = {
            'config_id': i+1,
            'auc': mean_auc,
            'config': config,
            'fold_aucs': fold_aucs,
            'timestamp': datetime.now().isoformat()
        }
        results.append(result)
        
        # 更新最佳配置
        if mean_auc > best_auc:
            best_auc = mean_auc
            best_config = config
            print(f"🏆 新的最佳配置! AUC: {best_auc:.4f}")
    
    # 保存结果
    results_file = os.path.join(args.output_dir, 'optimization_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # 保存最佳配置
    best_config_file = os.path.join(args.output_dir, 'best_config.json')
    with open(best_config_file, 'w') as f:
        json.dump({
            'best_auc': best_auc,
            'best_config': best_config,
            'baseline_auc': 0.675,
            'improvement': best_auc - 0.675
        }, f, indent=2, default=str)
    
    # 输出总结
    print(f"\n{'='*60}")
    print("🎯 模型优化完成")
    print(f"📊 基线 AUC: 0.675")
    print(f"🏆 最佳 AUC: {best_auc:.4f}")
    print(f"📈 改善: {best_auc - 0.675:+.4f}")
    print(f"💾 结果保存到: {results_file}")
    print(f"🔧 最佳配置保存到: {best_config_file}")

if __name__ == "__main__":
    main()
