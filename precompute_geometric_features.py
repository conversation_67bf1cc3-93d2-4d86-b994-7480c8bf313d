#!/usr/bin/env python3
"""
预计算几何特征脚本
为所有蛋白质预计算几何特征并保存，避免训练时的维度不一致问题
"""

import os
import pickle
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm

def compute_geometric_features(pos):
    """
    计算几何特征
    Args:
        pos: [N, 3] 节点3D坐标
    Returns:
        geometric_features: [N, 3] 几何特征
    """
    try:
        if pos.size(0) <= 1:
            return torch.zeros(pos.size(0), 3)
        
        # 计算质心
        centroid = pos.mean(dim=0, keepdim=True)  # [1, 3]
        
        # 质心距离特征
        centroid_dist = torch.norm(pos - centroid, dim=1, keepdim=True)  # [N, 1]
        
        # 最近邻距离特征
        pairwise_dist = torch.cdist(pos, pos)  # [N, N]
        # 排除自身距离
        pairwise_dist = pairwise_dist + torch.eye(pos.size(0)) * 1e6
        min_dist, _ = pairwise_dist.min(dim=1, keepdim=True)  # [N, 1]
        
        # 平均距离特征
        mean_dist = pairwise_dist.mean(dim=1, keepdim=True)  # [N, 1]
        
        # 组合几何特征 [N, 3]
        geometric_features = torch.cat([centroid_dist, min_dist, mean_dist], dim=1)
        
        # 归一化处理
        geometric_features = torch.clamp(geometric_features, min=0.1, max=100.0)
        
        # 标准化
        mean_vals = geometric_features.mean(dim=0, keepdim=True)
        std_vals = geometric_features.std(dim=0, keepdim=True) + 1e-6
        geometric_features = (geometric_features - mean_vals) / std_vals
        
        return geometric_features
        
    except Exception as e:
        print(f"⚠️ Error computing geometric features: {e}")
        return torch.zeros(pos.size(0), 3)

def load_protein_data():
    """加载蛋白质数据"""
    # 数据路径
    dataset_path = './datasets/'
    feature_path = './feature/'

    # 读取训练数据
    train_df = pd.read_csv(dataset_path + 'PRO_Train335.csv')

    # 加载蛋白质数据
    protein_data = {}
    for pdb_id in tqdm(train_df['ID'].unique(), desc="Loading protein data"):
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f"{pdb_id}_X.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_node_feature.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_mask.tensor", weights_only=True),
            )
        except Exception as e:
            print(f"⚠️ Failed to load {pdb_id}: {e}")

    return protein_data

def main():
    """预计算所有蛋白质的几何特征"""

    print("🚀 Starting geometric feature precomputation...")

    # 加载蛋白质数据
    print("📊 Loading protein data...")
    protein_data = load_protein_data()
    
    # 创建输出目录
    output_dir = "geometric_features"
    os.makedirs(output_dir, exist_ok=True)
    
    # 预计算几何特征
    geometric_features_dict = {}
    failed_proteins = []
    
    print(f"🔧 Computing geometric features for {len(protein_data)} proteins...")
    
    for protein_id, data in tqdm(protein_data.items(), desc="Computing geometric features"):
        try:
            # 获取3D坐标 (data是tuple: (X, node_features, mask))
            pos = data[0]  # X坐标
            if not isinstance(pos, torch.Tensor):
                pos = torch.tensor(pos, dtype=torch.float32)
            
            # 计算几何特征
            geometric_features = compute_geometric_features(pos)
            
            # 保存几何特征
            geometric_features_dict[protein_id] = geometric_features.numpy()
            
        except Exception as e:
            print(f"❌ Failed to process {protein_id}: {e}")
            failed_proteins.append(protein_id)
    
    # 保存预计算的几何特征
    output_file = os.path.join(output_dir, "geometric_features.pkl")
    with open(output_file, 'wb') as f:
        pickle.dump(geometric_features_dict, f)
    
    print(f"✅ Geometric features saved to {output_file}")
    print(f"📊 Successfully processed: {len(geometric_features_dict)} proteins")
    print(f"❌ Failed to process: {len(failed_proteins)} proteins")
    
    if failed_proteins:
        print("Failed proteins:", failed_proteins[:10])  # 显示前10个失败的
    
    # 验证数据
    print("\n🔍 Verifying saved data...")
    with open(output_file, 'rb') as f:
        loaded_data = pickle.load(f)
    
    print(f"✅ Loaded {len(loaded_data)} protein geometric features")
    
    # 显示统计信息
    if loaded_data:
        sample_protein = list(loaded_data.keys())[0]
        sample_features = loaded_data[sample_protein]
        print(f"📊 Sample protein {sample_protein}: shape {sample_features.shape}")
        print(f"📊 Feature statistics: mean={sample_features.mean():.4f}, std={sample_features.std():.4f}")

if __name__ == "__main__":
    main()
