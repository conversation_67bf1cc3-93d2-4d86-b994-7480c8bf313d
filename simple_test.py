#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 简单测试脚本
验证模型和数据是否正常工作
"""

import torch
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
from torch.utils.data import DataLoader

# 导入模型和工具
from model_gte import MVGNN_GTE
from utils import TaskDataset
from focalLoss import <PERSON>ocal<PERSON><PERSON>

def simple_test():
    """简单测试"""
    
    print("🔍 开始简单测试")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    df = pd.read_csv('./datasets/PRO_Train335.csv')
    print(f"数据集大小: {len(df)}")
    
    # 加载蛋白质特征
    protein_data = {}
    feature_path = './feature/'
    
    for pdb_id in df['ID'].unique()[:20]:  # 只加载前20个进行测试
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f'{pdb_id}_X.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_node_feature.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_mask.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_label.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_adj.tensor', weights_only=True)
            )
        except FileNotFoundError:
            continue
    
    print(f"加载了 {len(protein_data)} 个蛋白质特征")
    
    # 过滤数据
    df_filtered = df[df['ID'].isin(protein_data.keys())].reset_index(drop=True)
    print(f"过滤后数据集大小: {len(df_filtered)}")
    
    # 检查标签分布
    total_pos = 0
    total_neg = 0
    for pdb_id in protein_data.keys():
        _, _, mask, labels, _ = protein_data[pdb_id]
        valid_labels = labels[mask.bool()]
        pos_count = (valid_labels == 1).sum().item()
        neg_count = (valid_labels == 0).sum().item()
        total_pos += pos_count
        total_neg += neg_count
        print(f"{pdb_id}: {pos_count}正/{neg_count}负")
    
    print(f"总计: {total_pos}正/{total_neg}负, 比例: {total_pos/(total_pos+total_neg):.4f}")
    
    if total_pos == 0:
        print("❌ 没有正样本！这是问题所在！")
        return
    
    # 分割数据
    train_df, val_df = train_test_split(df_filtered, test_size=0.3, random_state=42)
    train_df = train_df.reset_index(drop=True)
    val_df = val_df.reset_index(drop=True)
    
    print(f"训练集大小: {len(train_df)}")
    print(f"验证集大小: {len(val_df)}")
    
    # 创建数据集
    train_dataset = TaskDataset(
        train_df, protein_data, 'label',
        use_graph_format=False,  # 使用标准格式
        use_geometric_features=True,
        training=True,
        use_golden_features=False
    )
    
    val_dataset = TaskDataset(
        val_df, protein_data, 'label',
        use_graph_format=False,  # 使用标准格式
        use_geometric_features=True,
        training=False,
        use_golden_features=False
    )
    
    print(f"训练数据集大小: {len(train_dataset)}")
    print(f"验证数据集大小: {len(val_dataset)}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=2,
        shuffle=True,
        collate_fn=train_dataset.collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=2,
        shuffle=False,
        collate_fn=val_dataset.collate_fn
    )
    
    # 检查一个批次
    print("\n🔍 检查训练批次:")
    for batch in train_loader:
        print(f"批次类型: {type(batch)}")
        if isinstance(batch, tuple):
            pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
            print(f"批次大小: {len(pdb_ids)}")
            print(f"protein_X: {protein_X.shape}")
            print(f"protein_node_features: {protein_node_features.shape}")
            print(f"protein_masks: {protein_masks.shape}")
            print(f"labels: {labels.shape}")
            print(f"adj: {adj.shape}")
            
            # 检查有效标签
            valid_mask = protein_masks.bool()
            valid_labels = labels[valid_mask]
            pos_count = (valid_labels == 1).sum().item()
            neg_count = (valid_labels == 0).sum().item()
            print(f"有效标签: {pos_count}正/{neg_count}负")
            
            if pos_count == 0:
                print("❌ 批次中没有正样本！")
            else:
                print("✅ 批次中有正样本")
        break
    
    # 创建模型
    print("\n🔍 创建模型:")
    model = MVGNN_GTE(
        node_features=1297,
        edge_features=16,
        hidden_dim=96,
        num_encoder_layers=4,
        k_neighbors=30,
        augment_eps=0.0,
        dropout=0.3,
        num_heads=4,
        egnn_layers=1,
        use_coords_update=False,
        use_unet_gt=True,
        pooling_ratio=0.3,
        use_geometric_features=True,
        num_iterations=1,
        use_global_node=True
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建损失函数和优化器
    pos_weight = torch.tensor([5.4]).to(device)
    bce_loss = torch.nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    focal_loss = FocalLoss(alpha=0.75, gamma=3.0)
    
    def combined_loss(outputs, targets):
        focal = focal_loss(outputs, targets)
        bce = bce_loss(outputs, targets)
        return 0.7 * focal + 0.3 * bce
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.0005, weight_decay=1e-5)
    
    # 训练几个epoch
    print("\n🔍 开始训练:")
    model.train()
    
    for epoch in range(5):
        print(f"\nEpoch {epoch+1}:")
        train_loss = 0.0
        batch_count = 0
        
        for batch_idx, batch in enumerate(train_loader):
            try:
                # 处理元组格式
                if isinstance(batch, tuple) and len(batch) == 6:
                    pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
                    
                    # 移动到设备
                    protein_X = protein_X.to(device)
                    protein_node_features = protein_node_features.to(device)
                    protein_masks = protein_masks.to(device)
                    labels = labels.to(device)
                    adj = adj.to(device)
                    
                    # 前向传播
                    optimizer.zero_grad()
                    outputs = model(protein_X, protein_node_features, protein_masks, adj)
                    
                    print(f"  批次 {batch_idx+1}: 输出形状={outputs.shape}")
                    
                    # 只对有效位置计算损失
                    valid_mask = protein_masks.bool()
                    valid_outputs = outputs[valid_mask]
                    valid_labels = labels[valid_mask]
                    
                    print(f"    有效输出: {valid_outputs.shape}, 有效标签: {valid_labels.shape}")
                    print(f"    输出范围: [{valid_outputs.min().item():.4f}, {valid_outputs.max().item():.4f}]")
                    print(f"    标签统计: 正={torch.sum(valid_labels == 1).item()}, 负={torch.sum(valid_labels == 0).item()}")
                    
                    if torch.sum(valid_labels == 1).item() == 0:
                        print("    ⚠️ 批次中没有正样本，跳过")
                        continue
                    
                    # 计算损失
                    loss = combined_loss(valid_outputs, valid_labels)
                    print(f"    损失: {loss.item():.4f}")
                    
                    # 反向传播
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    train_loss += loss.item()
                    batch_count += 1
                    
                    if batch_idx >= 2:  # 只处理前3个批次
                        break
                        
            except Exception as e:
                print(f"    批次 {batch_idx+1} 错误: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        avg_loss = train_loss / batch_count if batch_count > 0 else 0
        print(f"  平均损失: {avg_loss:.4f}")
        
        # 最后一个epoch进行验证
        if epoch == 4:
            print("\n🔍 验证阶段:")
            model.eval()
            val_predictions = []
            val_labels = []
            
            with torch.no_grad():
                for batch_idx, batch in enumerate(val_loader):
                    try:
                        if isinstance(batch, tuple) and len(batch) == 6:
                            pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
                            
                            # 移动到设备
                            protein_X = protein_X.to(device)
                            protein_node_features = protein_node_features.to(device)
                            protein_masks = protein_masks.to(device)
                            labels = labels.to(device)
                            adj = adj.to(device)
                            
                            outputs = model(protein_X, protein_node_features, protein_masks, adj)
                            
                            # 只对有效位置进行预测
                            valid_mask = protein_masks.bool()
                            valid_outputs = outputs[valid_mask]
                            valid_labels_tensor = labels[valid_mask]
                            
                            predictions = torch.sigmoid(valid_outputs).cpu().numpy()
                            labels_np = valid_labels_tensor.cpu().numpy()
                            
                            val_predictions.extend(predictions)
                            val_labels.extend(labels_np)
                            
                            print(f"  验证批次 {batch_idx+1}: 预测范围=[{predictions.min():.4f}, {predictions.max():.4f}]")
                            print(f"    标签统计: 正={np.sum(labels_np == 1)}, 负={np.sum(labels_np == 0)}")
                            
                            if batch_idx >= 2:  # 只处理前3个批次
                                break
                                
                    except Exception as e:
                        print(f"    验证批次 {batch_idx+1} 错误: {e}")
                        continue
            
            # 计算AUC
            if len(val_predictions) > 0 and len(set(val_labels)) > 1:
                val_auc = roc_auc_score(val_labels, val_predictions)
                print(f"  验证AUC: {val_auc:.4f}")
                print(f"  预测统计: 均值={np.mean(val_predictions):.4f}, 标准差={np.std(val_predictions):.4f}")
                print(f"  标签统计: 正样本={np.sum(np.array(val_labels) == 1)}, 负样本={np.sum(np.array(val_labels) == 0)}")
                
                if val_auc > 0.5:
                    print("✅ AUC > 0.5，模型正常工作！")
                else:
                    print("⚠️ AUC <= 0.5，可能还有问题")
            else:
                print(f"  无法计算AUC: 预测数={len(val_predictions)}, 标签类别数={len(set(val_labels))}")

if __name__ == "__main__":
    simple_test()
