#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 配置测试脚本
使用现有的训练脚本测试不同配置
"""

import subprocess
import json
import os
from datetime import datetime

def run_training_config(config_name, config_params, output_dir):
    """运行单个配置的训练"""
    
    # 构建命令
    cmd = [
        'python', 'train_optimal.py',
        '--epochs', str(config_params.get('epochs', 15)),
        '--folds', '2',  # 只用2折快速测试
        '--batch_size', str(config_params.get('batch_size', 4)),
        '--output_root', f'{output_dir}/{config_name}'
    ]
    
    # 添加其他参数
    if 'learning_rate' in config_params:
        cmd.extend(['--learning_rate', str(config_params['learning_rate'])])
    
    print(f"\n🔧 测试配置: {config_name}")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 运行训练
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            # 解析结果
            output_lines = result.stdout.split('\n')
            auc_line = None
            for line in output_lines:
                if '平均AUC:' in line:
                    auc_line = line
                    break
            
            if auc_line:
                # 提取AUC值
                auc_str = auc_line.split('平均AUC:')[1].split('±')[0].strip()
                auc = float(auc_str)
                print(f"✅ {config_name} 完成, AUC: {auc:.4f}")
                return auc
            else:
                print(f"⚠️ {config_name} 无法解析AUC")
                return 0.0
        else:
            print(f"❌ {config_name} 训练失败")
            print(f"错误: {result.stderr}")
            return 0.0
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {config_name} 训练超时")
        return 0.0
    except Exception as e:
        print(f"❌ {config_name} 运行错误: {e}")
        return 0.0

def main():
    # 创建输出目录
    output_dir = './config_test_results'
    os.makedirs(output_dir, exist_ok=True)
    
    # 基线配置 (当前最佳: AUC 0.675)
    baseline_config = {
        'epochs': 15,
        'batch_size': 4,
        'learning_rate': 0.0002
    }
    
    # 定义要测试的配置
    configs = {
        'baseline': baseline_config,
        
        # 学习率优化
        'lr_0001': {**baseline_config, 'learning_rate': 0.0001},
        'lr_0005': {**baseline_config, 'learning_rate': 0.0005},
        'lr_001': {**baseline_config, 'learning_rate': 0.001},
        
        # 批次大小优化
        'batch_2': {**baseline_config, 'batch_size': 2},
        'batch_6': {**baseline_config, 'batch_size': 6},
        'batch_8': {**baseline_config, 'batch_size': 8},
        
        # 训练轮数优化
        'epochs_20': {**baseline_config, 'epochs': 20},
        'epochs_25': {**baseline_config, 'epochs': 25},
        
        # 组合优化
        'combo_1': {**baseline_config, 'learning_rate': 0.0005, 'batch_size': 6},
        'combo_2': {**baseline_config, 'learning_rate': 0.0001, 'epochs': 20},
        'combo_3': {**baseline_config, 'learning_rate': 0.001, 'batch_size': 2},
    }
    
    print("🚀 开始配置测试")
    print(f"基线AUC: 0.675")
    print(f"总共 {len(configs)} 个配置需要测试")
    
    # 测试所有配置
    results = {}
    best_auc = 0.675  # 基线AUC
    best_config = 'baseline'
    
    for config_name, config_params in configs.items():
        auc = run_training_config(config_name, config_params, output_dir)
        results[config_name] = {
            'auc': auc,
            'config': config_params,
            'improvement': auc - 0.675,
            'timestamp': datetime.now().isoformat()
        }
        
        if auc > best_auc:
            best_auc = auc
            best_config = config_name
            print(f"🏆 新的最佳配置: {config_name}, AUC: {best_auc:.4f}")
    
    # 保存结果
    results_file = os.path.join(output_dir, 'config_test_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # 输出总结
    print(f"\n{'='*60}")
    print("🎯 配置测试完成")
    print(f"📊 基线 AUC: 0.675")
    print(f"🏆 最佳 AUC: {best_auc:.4f} ({best_config})")
    print(f"📈 改善: {best_auc - 0.675:+.4f}")
    print(f"💾 结果保存到: {results_file}")
    
    # 显示前5名配置
    print(f"\n📊 配置排名:")
    sorted_results = sorted(results.items(), key=lambda x: x[1]['auc'], reverse=True)
    for i, (name, result) in enumerate(sorted_results[:5]):
        print(f"{i+1}. {name}: AUC {result['auc']:.4f} (改善: {result['improvement']:+.4f})")
    
    # 保存最佳配置
    best_config_file = os.path.join(output_dir, 'best_config.json')
    with open(best_config_file, 'w') as f:
        json.dump({
            'best_config_name': best_config,
            'best_auc': best_auc,
            'best_config_params': results[best_config]['config'],
            'improvement': best_auc - 0.675,
            'all_results': sorted_results
        }, f, indent=2)
    
    print(f"🔧 最佳配置保存到: {best_config_file}")

if __name__ == "__main__":
    main()
