#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🚀 MVGNN-PPIS 性能突破版训练脚本
基于学习曲线诊断的系统性改进
"""
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
from tqdm import tqdm
import argparse
from model_gte import MVGNN_GTE
from utils import Seed_everything, Metric, Write_log, TaskDataset
from focalLoss import Focal<PERSON>oss
from noam_opt import get_std_opt

warnings.simplefilter('ignore')

def improved_focal_loss(outputs, targets, alpha=0.3, gamma=2.5):
    """改进的Focal Loss - 更强的类别平衡"""
    bce_loss = nn.BCEWithLogitsLoss(reduction='none')(outputs, targets.float())
    pt = torch.exp(-bce_loss)
    focal_loss = alpha * (1-pt)**gamma * bce_loss
    return focal_loss.mean()

def improved_combined_loss(outputs, targets):
    """改进的组合损失函数 - 调整权重比例"""
    focal = improved_focal_loss(outputs, targets)
    bce = nn.BCEWithLogitsLoss()(outputs, targets.float())
    return 0.6 * focal + 0.4 * bce  # 更重视focal loss

def validate_data_consistency(train_df, val_df):
    """验证数据分布一致性"""
    train_pos_ratio = train_df['label'].apply(lambda x: x.count('1') / len(x)).mean()
    val_pos_ratio = val_df['label'].apply(lambda x: x.count('1') / len(x)).mean()
    
    print(f"  训练集正样本比例: {train_pos_ratio:.4f}")
    print(f"  验证集正样本比例: {val_pos_ratio:.4f}")
    print(f"  比例差异: {abs(train_pos_ratio - val_pos_ratio):.4f}")
    
    is_consistent = abs(train_pos_ratio - val_pos_ratio) < 0.1
    print(f"  数据一致性: {'✅ 通过' if is_consistent else '❌ 不一致'}")
    
    return is_consistent

def train_with_gradient_accumulation(model, dataloader, optimizer, criterion, 
                                   accumulation_steps=2, max_grad_norm=0.5, device='cuda'):
    """使用梯度累积的训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    optimizer.zero_grad()
    
    for i, batch in enumerate(dataloader):
        # 数据移动到设备
        for key in batch:
            if torch.is_tensor(batch[key]):
                batch[key] = batch[key].to(device)
        
        try:
            # 前向传播
            outputs = model(batch)
            targets = batch['labels']
            loss = criterion(outputs, targets)
            
            # 损失归一化
            loss = loss / accumulation_steps
            loss.backward()
            
            # 收集预测和标签
            with torch.no_grad():
                preds = torch.sigmoid(outputs).cpu().numpy()
                labels = targets.cpu().numpy()
                all_preds.extend(preds.flatten())
                all_labels.extend(labels.flatten())
            
            # 梯度累积
            if (i + 1) % accumulation_steps == 0:
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
                optimizer.step()
                optimizer.zero_grad()
            
            total_loss += loss.item() * accumulation_steps
            
        except Exception as e:
            print(f"训练批次 {i} 出错: {e}")
            continue
    
    # 计算指标
    avg_loss = total_loss / len(dataloader)
    try:
        from sklearn.metrics import roc_auc_score
        auc_score = roc_auc_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.5
    except:
        auc_score = 0.5
    
    return avg_loss, auc_score

def train_model_improved(train_df, val_df, protein_data, model_class, config, fold, output_root='./output_improved/', args=None):
    """
    🚀 改进的模型训练函数 - 实施性能突破策略
    """
    print(f"\n🚀 开始训练 Fold {fold} (改进版)")
    print("-" * 50)
    
    # 数据一致性检查
    print("🔍 数据一致性检查:")
    is_consistent = validate_data_consistency(train_df, val_df)
    if not is_consistent:
        print("⚠️ 警告: 数据分布不一致，可能影响性能")
    
    # Setup paths and logging
    output_weight = output_root + "weight/"
    if not os.path.exists(output_weight):
        os.makedirs(output_weight)
    
    log = open(output_weight + f'fold{fold}.log', 'w', buffering=1)
    Write_log(log, str(config) + '\n')
    Write_log(log, f"数据一致性: {is_consistent}\n")
    
    # 🚀 改进配置参数
    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config.get('hidden_dim', 128)  # 增加到128
    num_encoder_layers = config.get('num_encoder_layers', 5)  # 增加到5
    k_neighbors = config['k_neighbors']
    augment_eps = config.get('augment_eps', 0.0)
    dropout = config.get('dropout', 0.4)  # 增加到0.4
    batch_size = config.get('batch_size', 8)  # 增加到8
    epochs = config.get('epochs', 40)  # 增加到40
    patience = config.get('patience', 12)  # 增加到12
    learning_rate = config.get('learning_rate', 2e-4)  # 降低到2e-4
    weight_decay = config.get('weight_decay', 1e-4)  # 增加到1e-4
    
    # 梯度累积参数
    accumulation_steps = config.get('gradient_accumulation_steps', 2)
    max_grad_norm = config.get('max_grad_norm', 0.5)
    
    print(f"📋 使用改进配置:")
    print(f"  hidden_dim: {hidden_dim}, num_layers: {num_encoder_layers}")
    print(f"  dropout: {dropout}, batch_size: {batch_size}")
    print(f"  learning_rate: {learning_rate}, weight_decay: {weight_decay}")
    print(f"  梯度累积步数: {accumulation_steps}")
    
    # Reset DataFrame indices
    train_df_reset = train_df.reset_index(drop=True)
    val_df_reset = val_df.reset_index(drop=True)

    # 🚀 关键改进: 强制使用图格式
    train_dataset = TaskDataset(train_df_reset, protein_data, 'label', 
                               use_graph_format=True,  # 强制图格式
                               use_geometric_features=True, 
                               k_neighbors=k_neighbors, augment_eps=augment_eps)
    
    val_dataset = TaskDataset(val_df_reset, protein_data, 'label', 
                             use_graph_format=True,  # 强制图格式
                             use_geometric_features=True, 
                             k_neighbors=k_neighbors, augment_eps=0.0)
    
    # 数据加载器
    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, 
                                 collate_fn=train_dataset.collate_fn, num_workers=0)
    val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, 
                               collate_fn=val_dataset.collate_fn, num_workers=0)
    
    print(f"训练集大小: {len(train_dataset)}, 验证集大小: {len(val_dataset)}")
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model_class(
        node_features=node_features,
        edge_features=edge_features,
        hidden_dim=hidden_dim,
        num_encoder_layers=num_encoder_layers,
        k_neighbors=k_neighbors,
        augment_eps=augment_eps,
        dropout=dropout,
        use_unet_gt=config.get('use_unet_gt', True),
        pooling_ratio=config.get('pooling_ratio', 0.5),
        use_graph_collapse=config.get('use_graph_collapse', True),
        use_geometric_features=config.get('use_geometric_features', False),
        num_iterations=config.get('num_iterations', 2),
        use_global_node=config.get('use_global_node', True)
    ).to(device)
    
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 🚀 改进的优化器和调度器
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=learning_rate,
        weight_decay=weight_decay
    )
    
    # 使用更稳定的学习率调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, 
        verbose=True, min_lr=1e-6
    )
    
    # 改进的损失函数
    criterion = improved_combined_loss

    # 训练循环
    best_val_auc = 0
    patience_counter = 0
    train_losses = []
    val_losses = []
    train_aucs = []
    val_aucs = []

    Write_log(log, "开始训练循环...\n")

    for epoch in range(epochs):
        # 🚀 使用梯度累积训练
        train_loss, train_auc = train_with_gradient_accumulation(
            model, train_dataloader, optimizer, criterion,
            accumulation_steps=accumulation_steps,
            max_grad_norm=max_grad_norm,
            device=device
        )

        # 验证
        model.eval()
        val_loss = 0
        all_val_preds = []
        all_val_labels = []

        with torch.no_grad():
            for batch in val_dataloader:
                for key in batch:
                    if torch.is_tensor(batch[key]):
                        batch[key] = batch[key].to(device)

                try:
                    outputs = model(batch)
                    targets = batch['labels']
                    loss = criterion(outputs, targets)
                    val_loss += loss.item()

                    preds = torch.sigmoid(outputs).cpu().numpy()
                    labels = targets.cpu().numpy()
                    all_val_preds.extend(preds.flatten())
                    all_val_labels.extend(labels.flatten())
                except Exception as e:
                    print(f"验证批次出错: {e}")
                    continue

        val_loss /= len(val_dataloader)

        # 计算验证AUC
        try:
            from sklearn.metrics import roc_auc_score
            val_auc = roc_auc_score(all_val_labels, all_val_preds) if len(set(all_val_labels)) > 1 else 0.5
        except:
            val_auc = 0.5

        # 学习率调度
        scheduler.step(val_auc)
        current_lr = optimizer.param_groups[0]['lr']

        # 记录指标
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_aucs.append(train_auc)
        val_aucs.append(val_auc)

        # 日志记录
        log_msg = (f"Epoch {epoch+1}/{epochs} - "
                  f"Train Loss: {train_loss:.4f}, Train AUC: {train_auc:.4f}, "
                  f"Val Loss: {val_loss:.4f}, Val AUC: {val_auc:.4f}, "
                  f"LR: {current_lr:.2e}")

        print(log_msg)
        Write_log(log, log_msg + '\n')

        # 早停和模型保存
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience_counter = 0

            # 保存最佳模型
            model_save_path = output_weight + f'best_model_fold{fold}.pth'
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'best_val_auc': best_val_auc,
                'config': config
            }, model_save_path)

            Write_log(log, f"新的最佳模型已保存，AUC: {best_val_auc:.4f}\n")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                early_stop_msg = f"早停触发，最佳验证AUC: {best_val_auc:.4f}"
                print(early_stop_msg)
                Write_log(log, early_stop_msg + '\n')
                break

    # 最终评估
    Write_log(log, f"\nFold {fold} 训练完成\n")
    Write_log(log, f"最佳验证AUC: {best_val_auc:.4f}\n")
    Write_log(log, f"数据一致性: {is_consistent}\n")

    log.close()

    return best_val_auc, {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'train_aucs': train_aucs,
        'val_aucs': val_aucs,
        'best_val_auc': best_val_auc,
        'data_consistent': is_consistent
    }

def main():
    """主函数 - 运行改进的5折交叉验证"""
    parser = argparse.ArgumentParser(description='MVGNN-PPIS 性能突破训练')

    # 🚀 改进的默认参数
    parser.add_argument('--hidden_dim', type=int, default=128, help='隐藏层维度')
    parser.add_argument('--num_encoder_layers', type=int, default=5, help='编码器层数')
    parser.add_argument('--dropout', type=float, default=0.4, help='Dropout率')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小')
    parser.add_argument('--epochs', type=int, default=40, help='训练轮数')
    parser.add_argument('--patience', type=int, default=12, help='早停耐心值')
    parser.add_argument('--learning_rate', type=float, default=2e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--gradient_accumulation_steps', type=int, default=2, help='梯度累积步数')
    parser.add_argument('--max_grad_norm', type=float, default=0.5, help='梯度裁剪阈值')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    print("🚀 MVGNN-PPIS 性能突破训练开始")
    print("=" * 60)
    print("基于学习曲线诊断的系统性改进方案")
    print("=" * 60)

    # 设置随机种子
    Seed_everything(args.seed)

    # 🚀 BREAKTHROUGH_CONFIG - 性能突破配置
    config = {
        'node_features': 1297,  # ESM2 (1280) + DSSP (14) + geometric (3)
        'edge_features': 16,
        'hidden_dim': args.hidden_dim,
        'num_encoder_layers': args.num_encoder_layers,
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': args.dropout,
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'patience': args.patience,
        'learning_rate': args.learning_rate,
        'weight_decay': args.weight_decay,
        'gradient_accumulation_steps': args.gradient_accumulation_steps,
        'max_grad_norm': args.max_grad_norm,

        # 模型架构参数
        'use_unet_gt': True,
        'pooling_ratio': 0.5,
        'use_graph_collapse': True,
        'use_geometric_features': False,
        'num_iterations': 2,
        'use_global_node': True,
    }

    print("📋 突破性配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # 加载数据
    print("\n📂 加载数据...")
    train_df = pd.read_csv('./datasets/PRO_Train335.csv')
    protein_data = np.load('./datasets/protein_data.npz', allow_pickle=True)

    print(f"数据集大小: {len(train_df)}")
    print(f"蛋白质数据: {len(protein_data.files)} 个文件")

    # 5折交叉验证
    kf = KFold(n_splits=5, shuffle=True, random_state=args.seed)
    fold_results = []
    all_metrics = []

    output_root = './output_breakthrough/'

    for fold, (train_idx, val_idx) in enumerate(kf.split(train_df), 1):
        print(f"\n{'='*20} Fold {fold} {'='*20}")

        train_fold_df = train_df.iloc[train_idx]
        val_fold_df = train_df.iloc[val_idx]

        # 训练模型
        best_auc, metrics = train_model_improved(
            train_fold_df, val_fold_df, protein_data,
            MVGNN_GTE, config, fold, output_root, args
        )

        fold_results.append(best_auc)
        all_metrics.append(metrics)

        print(f"Fold {fold} 最佳AUC: {best_auc:.4f}")
        print(f"数据一致性: {metrics['data_consistent']}")

    # 总结结果
    print(f"\n🎯 5折交叉验证结果总结:")
    print("=" * 60)
    print(f"各fold AUC: {[f'{auc:.4f}' for auc in fold_results]}")
    print(f"平均AUC: {np.mean(fold_results):.4f} ± {np.std(fold_results):.4f}")
    print(f"最佳AUC: {np.max(fold_results):.4f}")
    print(f"最差AUC: {np.min(fold_results):.4f}")

    # 数据一致性统计
    consistent_folds = sum(1 for m in all_metrics if m['data_consistent'])
    print(f"数据一致的fold数: {consistent_folds}/5")

    # 保存结果
    results_path = output_root + 'breakthrough_results.txt'
    with open(results_path, 'w') as f:
        f.write("MVGNN-PPIS 性能突破结果\n")
        f.write("=" * 40 + "\n")
        f.write(f"配置: {config}\n\n")
        f.write(f"各fold AUC: {fold_results}\n")
        f.write(f"平均AUC: {np.mean(fold_results):.4f} ± {np.std(fold_results):.4f}\n")
        f.write(f"数据一致的fold数: {consistent_folds}/5\n")

    print(f"\n✅ 结果已保存到: {results_path}")

    if np.mean(fold_results) > 0.75:
        print("🎉 性能突破成功！平均AUC > 0.75")
    elif np.std(fold_results) < 0.05:
        print("✅ 训练稳定性显著改善！AUC标准差 < 0.05")
    else:
        print("📈 仍有改进空间，建议进一步调优")

if __name__ == "__main__":
    main()
