
import torch
import torch.nn as nn
import torch.nn.functional as F
from edge_features import EdgeFeatures
from gte_block import GTE_Block


class MVGNN_GTE(nn.Module):
    """
    升级版MVGNN模型，集成了GTE-PPIS的核心思想
    
    主要改进：
    1. 用GTE_Block替换原来的TransformerLayer
    2. GTE_Block包含两个并行分支：EGC层(结构) + GT层(特征)
    3. 支持坐标更新，保持几何等变性
    4. 更强的边特征利用
    """
    
    def __init__(self, node_features, edge_features, hidden_dim,
                 num_encoder_layers=4, k_neighbors=30, augment_eps=0.,
                 dropout=0.2, num_heads=4, egnn_layers=2, use_coords_update=True,
                 use_unet_gt=False, pooling_ratio=0.5, use_graph_collapse=True,
                 use_geometric_features=False, num_iterations=2, use_global_node=True):
        super(MVGNN_GTE, self).__init__()

        # 超参数
        self.augment_eps = augment_eps
        self.use_coords_update = use_coords_update
        self.use_unet_gt = use_unet_gt
        self.pooling_ratio = pooling_ratio
        self.use_geometric_features = use_geometric_features
        self.num_iterations = num_iterations  # 🚀 新增：迭代式协同进化的迭代次数
        self.use_global_node = use_global_node  # 🚀 新增：是否使用全局上下文节点

        # 🚀 动态计算实际输入特征维度
        self.base_node_features = node_features
        self.geometric_features_dim = 3 if use_geometric_features else 0
        # 🔧 修复：当use_geometric_features=True时，node_features已经包含了几何特征
        # 所以不需要再次添加几何特征维度
        self.actual_node_features = node_features  # 直接使用传入的node_features

        # 🔧 几何特征增强配置完成

        # 🚀 几何特征计算模块
        if use_geometric_features:
            self.geometric_feature_dim = 3
            self.geometric_processor = nn.Sequential(
                nn.Linear(3, 16),  # 3D坐标 -> 16维
                nn.ReLU(),
                nn.Linear(16, self.geometric_feature_dim),  # -> 3维几何特征
                nn.Tanh()  # 限制输出范围
            )
        else:
            self.geometric_feature_dim = 0
            self.geometric_processor = None

        # 🔧 关键修复：输入特征归一化（支持动态维度）
        self.input_norm = nn.LayerNorm(self.actual_node_features)

        # 🔧 数值稳定性：权重初始化
        self._init_weights()

        # 边特征化层
        self.EdgeFeatures = EdgeFeatures(edge_features, top_k=k_neighbors, augment_eps=augment_eps)
        self.dropout = nn.Dropout(dropout)

        # 🔧 重复的LayerNorm初始化已删除（使用上面的动态版本）

        # 嵌入层
        self.act_fn = nn.ReLU()
        self.lin1 = nn.Linear(self.actual_node_features, 512, bias=True)  # 支持动态特征维度
        self.lin2 = nn.Linear(512, 256, bias=False)
        self.W_v = nn.Linear(256, hidden_dim, bias=True)
        self.W_e = nn.Linear(edge_features, hidden_dim, bias=True)

        # 🚀 迭代式协同进化GTE编码器 - 融合成功MVGNN设计
        self.gte_layers = nn.ModuleList([
            GTE_Block(
                hidden_dim=hidden_dim,
                edge_dim=edge_features,
                num_heads=num_heads,
                dropout=dropout,
                egnn_layers=egnn_layers,
                attention=True,
                residual=True,
                use_unet_gt=use_unet_gt,
                pooling_ratio=pooling_ratio,
                num_iterations=1,  # 🚀 默认设为1以提高稳定性
                use_coords_update=use_coords_update,  # 🚀 可配置的坐标更新
                use_gcn_fusion=True  # 🚀 启用GCN融合
            )
            for _ in range(num_encoder_layers)
        ])

        # 输出层
        self.W_out1_standard = nn.Linear(hidden_dim, hidden_dim, bias=True)
        self.W_out2 = nn.Linear(hidden_dim, 1, bias=True)

        # 🚀 全局上下文节点嵌入
        if self.use_global_node:
            self.global_node_embedding = nn.Parameter(torch.randn(1, hidden_dim) * 0.1)
            print(f"✅ Initialized global context node embedding: {self.global_node_embedding.shape}")

        # 🔧 数值稳定性：权重初始化
        self._init_weights()

        # 初始化
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def _compute_geometric_features(self, pos):
        """
        在模型内部计算几何特征，确保所有样本处理一致
        Args:
            pos: [N, 3] 节点3D坐标
        Returns:
            geometric_features: [N, 3] 几何特征
        """
        if not self.use_geometric_features or self.geometric_processor is None:
            return torch.zeros(pos.size(0), 0).to(pos.device)

        try:
            # 计算质心
            centroid = pos.mean(dim=0, keepdim=True)  # [1, 3]

            # 质心距离特征
            centroid_dist = torch.norm(pos - centroid, dim=1, keepdim=True)  # [N, 1]

            # 最近邻距离特征
            if pos.size(0) > 1:
                pairwise_dist = torch.cdist(pos, pos)  # [N, N]
                # 排除自身距离
                pairwise_dist = pairwise_dist + torch.eye(pos.size(0)).to(pos.device) * 1e6
                min_dist, _ = pairwise_dist.min(dim=1, keepdim=True)  # [N, 1]
                mean_dist = pairwise_dist.mean(dim=1, keepdim=True)  # [N, 1]
            else:
                min_dist = torch.zeros_like(centroid_dist)
                mean_dist = torch.zeros_like(centroid_dist)

            # 组合原始几何特征 [N, 3]
            raw_geometric = torch.cat([centroid_dist, min_dist, mean_dist], dim=1)

            # 通过神经网络处理几何特征
            geometric_features = self.geometric_processor(raw_geometric)  # [N, 3]

            return geometric_features

        except Exception as e:
            # 计算失败时返回零特征
            print(f"⚠️ Geometric feature computation failed: {e}")
            return torch.zeros(pos.size(0), self.geometric_feature_dim).to(pos.device)

    def forward(self, X=None, V=None, mask=None, adj=None, batch=None):
        """
        🚀 高效前向传播 - 支持图格式和标准格式

        Args:
            X: 3D坐标矩阵 [B, L, 3] 或图格式时为 None
            V: 预计算的节点特征 [B, L, node_features] 或图格式时为 None
            mask: 有效位置掩码 [B, L] 或图格式时为 None
            adj: 邻接矩阵 [B, L, L] 或图格式时为 None
            batch: torch_geometric.data.Batch 对象（图格式）

        Returns:
            logits: 每个位置的结合概率 [B, L] 或图格式
        """

        # 🚀 检查是否是图格式输入
        if batch is not None and hasattr(batch, 'x'):
            return self._forward_graph_format(batch)
        else:
            return self._forward_standard_format(X, V, mask, adj)

    def _forward_graph_format(self, batch):
        """🚀 图格式的高效前向传播"""
        # 🔧 修复：创建batch的副本以避免修改原始输入
        # 创建batch副本
        batch_copy = batch.clone()

        # 从batch对象获取数据
        V_flat = batch_copy.x    # [total_nodes, node_features]

        # 🔧 阶段一优化：输入特征归一化
        V_flat = self.input_norm(V_flat)

        # 坐标增强（如果在训练模式）
        if self.training and self.augment_eps > 0:
            V_flat = V_flat + 0.1 * self.augment_eps * torch.randn_like(V_flat)

        # 节点特征嵌入
        V_embedded = self.act_fn(self.lin1(V_flat))
        V_embedded = self.act_fn(self.lin2(V_embedded))
        h_V_flat = self.W_v(V_embedded)  # [total_nodes, hidden_dim]

        # 🚀 【终极修复】使用real_nodes_mask精确定位全局节点
        if self.use_global_node and hasattr(batch_copy, 'real_nodes_mask'):
            global_nodes_mask = ~batch_copy.real_nodes_mask
            if global_nodes_mask.any():
                # 直接使用布尔掩码定位所有全局节点，无论它们在批次中的哪个位置
                num_global_nodes = global_nodes_mask.sum()
                h_V_flat[global_nodes_mask] = self.global_node_embedding.expand(
                    num_global_nodes, -1
                )
                # print(f"✅ 成功替换 {num_global_nodes} 个全局节点特征")  # 🔧 注释掉调试输出

        # 更新batch副本
        batch_copy.x = h_V_flat

        # 🚀 修复：正确调用支持图格式的GTE编码器层
        # 通过GTE编码器层
        for i, layer in enumerate(self.gte_layers):
            batch_copy = layer(batch=batch_copy)  # GTE Block只返回batch对象

            # 可选：在训练时添加坐标噪声
            if self.training and self.augment_eps > 0 and i < len(self.gte_layers) - 1:
                if hasattr(batch_copy, 'pos') and batch_copy.pos is not None:
                    batch_copy.pos = batch_copy.pos + 0.01 * self.augment_eps * torch.randn_like(batch_copy.pos)

        # 🚀 【重构】输出预测：使用real_nodes_mask确保全局节点不参与预测
        if self.use_global_node and hasattr(batch_copy, 'real_nodes_mask'):
            real_mask = batch_copy.real_nodes_mask
            if real_mask is not None and (~real_mask).any():
                # 有全局节点，只对真实节点进行预测
                node_features_for_prediction = batch_copy.x[real_mask]
            else:
                # 没有全局节点或mask无效，使用所有节点
                node_features_for_prediction = batch_copy.x
        else:
            # 没有全局节点机制，使用所有节点
            node_features_for_prediction = batch_copy.x

        # 🚀 新增：利用更新后的坐标信息
        if hasattr(batch_copy, 'pos') and batch_copy.pos is not None:
            # 坐标信息已经通过EGNN融合到节点特征中
            h_out = self.W_out1_standard(node_features_for_prediction)
            # 🔧 更严格的数值控制
            h_out = torch.clamp(h_out, min=-5.0, max=5.0)
            logits_flat = self.act_fn(h_out)
            # 🔧 激活后再次裁剪
            logits_flat = torch.clamp(logits_flat, min=-3.0, max=3.0)
        else:
            # 标准预测路径
            h_out = self.W_out1_standard(node_features_for_prediction)
            # 🔧 更严格的数值控制
            h_out = torch.clamp(h_out, min=-5.0, max=5.0)
            logits_flat = self.act_fn(h_out)
            # 🔧 激活后再次裁剪
            logits_flat = torch.clamp(logits_flat, min=-3.0, max=3.0)

        logits_flat = self.W_out2(logits_flat).squeeze(-1)  # [num_real_nodes] 或 [total_nodes]

        # 🔧 最终输出层的严格控制
        logits_flat = torch.clamp(logits_flat, min=-3.0, max=3.0)

        # 🔧 强化数值稳定性保护
        logits_flat = torch.clamp(logits_flat, min=-5.0, max=5.0)

        # 🔧 检查异常值
        if torch.isnan(logits_flat).any() or torch.isinf(logits_flat).any():
            print(f"⚠️ Warning: Invalid logits detected, resetting to zero")
            logits_flat = torch.zeros_like(logits_flat)

        # 🚀 【修复】返回预测张量而不是batch对象
        # logits_flat已经是只包含真实节点的预测，直接返回
        return logits_flat

    def _forward_standard_format(self, X, V, mask, adj):
        """标准格式的前向传播（简化版本，建议使用图格式）"""
        # 注意：X, mask, adj参数保留用于接口兼容性，但在简化版本中不使用

        # 数据增强
        if self.training and self.augment_eps > 0:
            V = V + 0.1 * self.augment_eps * torch.randn_like(V)

        # 节点特征嵌入
        V = self.act_fn(self.lin1(V))
        V = self.act_fn(self.lin2(V))
        h_V = self.W_v(V)  # [B, L, hidden_dim]

        # 简化的编码器处理（保持向后兼容）
        # 注意：完整的GTE功能需要使用图格式 (--use_graph_format)
        for _ in range(len(self.gte_layers)):
            # 简单的特征变换
            h_V = h_V + 0.1 * torch.randn_like(h_V) if self.training else h_V

        # 输出预测
        h_V = self.dropout(h_V)
        h_out = self.act_fn(self.W_out1_standard(h_V))

        # 🚀 改进5：多头预测增强鲁棒性
        if self.training:
            # 训练时使用dropout创建多个预测头
            logits_list = []
            for _ in range(3):  # 3个预测头
                dropped_h = F.dropout(h_out, p=0.1, training=True)
                head_logits = self.W_out2(dropped_h).squeeze(-1)
                logits_list.append(head_logits)

            # 平均多个预测头的结果
            logits = torch.stack(logits_list).mean(0)
        else:
            # 推理时使用标准预测
            logits = self.W_out2(h_out).squeeze(-1)

        # 🔧 强化数值稳定性保护
        logits = torch.clamp(logits, min=-5.0, max=5.0)

        # 🔧 检查异常值
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            print(f"⚠️ Warning: Invalid logits detected, resetting to zero")
            logits = torch.zeros_like(logits)

        return logits

    def _init_weights(self):
        """🔧 修复权重初始化：平衡稳定性和学习能力"""
        for name, param in self.named_parameters():
            if 'weight' in name and param.dim() > 1:
                if 'fusion_gate' in name:
                    # 门控网络需要更小的初始化
                    nn.init.xavier_uniform_(param, gain=0.1)
                else:
                    # 其他层使用标准初始化
                    nn.init.xavier_uniform_(param, gain=1.0)
            elif 'bias' in name:
                nn.init.constant_(param, 0.0)

        # 🔧 输出层使用适中的初始化
        if hasattr(self, 'W_out1_standard'):
            nn.init.xavier_uniform_(self.W_out1_standard.weight, gain=0.1)
        if hasattr(self, 'W_out2'):
            nn.init.xavier_uniform_(self.W_out2.weight, gain=0.1)

    def get_updated_coordinates(self, X, V, mask, adj):
        """
        获取更新后的坐标（用于分析或可视化）
        
        Returns:
            coords_updated: 更新后的坐标 [B, L, 3]
            logits: 预测结果 [B, L]
        """
        # 这个方法允许我们同时获取预测结果和更新后的坐标
        E, E_idx = self.EdgeFeatures(X, mask)
        
        if self.training and self.augment_eps > 0:
            V = V + 0.1 * self.augment_eps * torch.randn_like(V)

        V = self.act_fn(self.lin1(V))
        V = self.act_fn(self.lin2(V))
        h_V = self.W_v(V)
        h_E = self.W_e(E)

        coords = X.clone()
        
        for layer in self.encoder_layers:
            h_V, coords = layer(h_V, coords, h_E, E_idx, mask_V=mask)

        logits = self.act_fn(self.W_out1(h_V))
        logits = self.W_out2(logits).squeeze(-1)
        
        return coords, logits


class MVGNN_GTE_Hybrid(nn.Module):
    """
    混合版本：同时保留原始MVGNN的一些组件
    
    这个版本可以让您逐步迁移，比较不同方法的效果
    """
    
    def __init__(self, node_features, edge_features, hidden_dim, 
                 num_encoder_layers=4, k_neighbors=30, augment_eps=0., 
                 dropout=0.2, use_gte_ratio=0.5):
        super(MVGNN_GTE_Hybrid, self).__init__()
        
        self.augment_eps = augment_eps
        self.use_gte_ratio = use_gte_ratio  # GTE层的比例
        
        # 共享的特征提取层
        self.EdgeFeatures = EdgeFeatures(edge_features, top_k=k_neighbors, augment_eps=augment_eps)
        self.dropout = nn.Dropout(dropout)
        self.act_fn = nn.ReLU()
        self.lin1 = nn.Linear(node_features, 512, bias=True)
        self.lin2 = nn.Linear(512, 256, bias=False)
        self.W_v = nn.Linear(256, hidden_dim, bias=True)
        self.W_e = nn.Linear(edge_features, hidden_dim, bias=True)
        
        # 混合编码器：部分使用GTE_Block，部分使用原始TransformerLayer
        num_gte_layers = int(num_encoder_layers * use_gte_ratio)
        num_original_layers = num_encoder_layers - num_gte_layers
        
        # GTE层
        self.gte_layers = nn.ModuleList([
            GTE_Block(
                hidden_dim=hidden_dim,
                edge_dim=hidden_dim,
                num_heads=4,
                dropout=dropout,
                egnn_layers=1,
                attention=True,
                residual=True
            ) for _ in range(num_gte_layers)
        ])
        
        # 原始Transformer层（需要从原始代码导入）
        # 这里用占位符，实际使用时需要导入原始的TransformerLayer
        self.original_layers = nn.ModuleList([
            # TransformerLayer(hidden_dim, hidden_dim * 2, dropout=dropout)
            # 为了演示，这里用简单的线性层替代
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for _ in range(num_original_layers)
        ])
        
        # 输出层
        self.W_out1 = nn.Linear(hidden_dim, 64, bias=True)
        self.W_out2 = nn.Linear(64, 1, bias=True)

        # 初始化
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, X, V, mask, adj):
        """混合前向传播"""
        E, E_idx = self.EdgeFeatures(X, mask)
        
        if self.training and self.augment_eps > 0:
            V = V + 0.1 * self.augment_eps * torch.randn_like(V)

        V = self.act_fn(self.lin1(V))
        V = self.act_fn(self.lin2(V))
        h_V = self.W_v(V)
        h_E = self.W_e(E)

        coords = X.clone()
        
        # 先通过GTE层
        for layer in self.gte_layers:
            h_V, coords = layer(h_V, coords, h_E, E_idx, mask_V=mask)
        
        # 再通过原始层（这里简化处理）
        for layer in self.original_layers:
            h_V = layer(h_V)
            if mask is not None:
                h_V = h_V * mask.unsqueeze(-1)

        logits = self.act_fn(self.W_out1(h_V))
        logits = self.W_out2(logits).squeeze(-1)
        
        return logits
