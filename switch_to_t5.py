#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 ESM2特征切换回T5特征的便捷脚本

使用方法:
1. 运行此脚本: python switch_to_t5.py
2. 重新训练模型

特征对比:
- ESM2: 1280维 → 总特征维度: 1294 (1280 + 14 DSSP)
- T5: 1024维 → 总特征维度: 1038 (1024 + 14 DSSP)
"""

import os
import sys
import shutil

def update_pad_feature_config():
    """更新pad_feature.py配置为使用T5"""
    pad_feature_path = "process_feature/pad_feature.py"
    
    if not os.path.exists(pad_feature_path):
        print(f"❌ 文件不存在: {pad_feature_path}")
        return False
    
    # 读取文件
    with open(pad_feature_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换配置
    content = content.replace("USE_ESM2 = True", "USE_ESM2 = False")
    
    # 写回文件
    with open(pad_feature_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已更新pad_feature.py配置为使用T5")
    return True

def restore_t5_features():
    """恢复T5特征"""
    backup_dir = "backup_T5_features"
    t5_dirs = ["T5raw", "T5norm"]
    
    if not os.path.exists(backup_dir):
        print(f"❌ 备份目录不存在: {backup_dir}")
        print("请确保之前已经备份了T5特征")
        return False
    
    # 备份当前ESM2特征
    esm2_backup_dir = "backup_ESM2_features"
    if not os.path.exists(esm2_backup_dir):
        os.makedirs(esm2_backup_dir, exist_ok=True)
        
        esm2_dirs = ["ESM2raw", "ESM2norm"]
        for esm2_dir in esm2_dirs:
            if os.path.exists(esm2_dir):
                shutil.move(esm2_dir, os.path.join(esm2_backup_dir, esm2_dir))
                print(f"✅ 已备份 {esm2_dir} 到 {esm2_backup_dir}")
    
    # 恢复T5特征
    for t5_dir in t5_dirs:
        backup_path = os.path.join(backup_dir, t5_dir)
        if os.path.exists(backup_path):
            if os.path.exists(t5_dir):
                shutil.rmtree(t5_dir)
            shutil.move(backup_path, t5_dir)
            print(f"✅ 已恢复 {t5_dir}")
    
    return True

def show_feature_comparison():
    """显示特征对比信息"""
    print("\n" + "="*60)
    print("📊 ESM2 vs T5 特征对比")
    print("="*60)
    print("ESM2特征:")
    print("  - 语言模型维度: 1280")
    print("  - DSSP特征维度: 14")
    print("  - 总特征维度: 1294")
    print("  - 模型大小: ~2.5GB")
    print()
    print("T5特征:")
    print("  - 语言模型维度: 1024")
    print("  - DSSP特征维度: 14")
    print("  - 总特征维度: 1038")
    print("  - 模型大小: ~3GB")
    print()
    print("T5优势:")
    print("  - 通用语言模型，在某些任务上可能表现更好")
    print("  - 特征维度较小，训练速度可能更快")
    print("  - 内存占用相对较少")
    print("="*60)

def main():
    """主函数"""
    print("🚀 ESM2特征切换回T5特征")
    print("="*50)
    
    # 显示特征对比
    show_feature_comparison()
    
    # 确认操作
    response = input("\n是否继续切换回T5特征？(y/N): ").strip().lower()
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    # 1. 更新配置
    print("\n🔄 更新配置文件...")
    update_pad_feature_config()
    
    # 2. 恢复T5特征
    print("\n🔄 恢复T5特征...")
    if not restore_t5_features():
        print("❌ T5特征恢复失败")
        return
    
    print("\n" + "="*50)
    print("✅ 成功切换回T5特征！")
    print("="*50)
    print("📝 接下来的步骤:")
    print("1. 检查特征文件是否正确恢复在 T5raw/ 和 T5norm/ 目录")
    print("2. 运行训练脚本: python train_optimal.py")
    print("3. 模型会自动检测并使用T5特征（1038维）")
    print()
    print("💡 如需再次切换到ESM2特征:")
    print("1. 运行: python switch_to_esm2.py")
    print("2. 或手动修改 process_feature/pad_feature.py 中的 USE_ESM2 = True")
    print("="*50)

if __name__ == "__main__":
    main()
