# 改进的MVGNN-PPIS配置
IMPROVED_CONFIG = {
    'learning_rate': 0.0002,
    'weight_decay': 0.0001,
    'dropout': 0.4,
    'hidden_dim': 128,
    'num_encoder_layers': 5,
    'batch_size': 8,
    'epochs': 40,
    'patience': 12,
    'use_graph_format': True,
    'use_gradient_accumulation': True,
    'gradient_accumulation_steps': 2,
    'max_grad_norm': 0.5,
    'use_modular_features': True,
    'use_feature_normalization': True,
    'node_features': 1297,
    'edge_features': 16,
    'k_neighbors': 30,
    'augment_eps': 0.0,
    'use_unet_gt': True,
    'pooling_ratio': 0.5,
    'use_graph_collapse': True,
    'use_geometric_features': False,
    'num_iterations': 2,
    'use_global_node': True,
}
