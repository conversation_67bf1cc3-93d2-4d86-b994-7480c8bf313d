<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="100">
            <item index="0" class="java.lang.String" itemvalue="sklearn" />
            <item index="1" class="java.lang.String" itemvalue="numba" />
            <item index="2" class="java.lang.String" itemvalue="torch-scatter" />
            <item index="3" class="java.lang.String" itemvalue="joblib" />
            <item index="4" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="5" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="6" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="7" class="java.lang.String" itemvalue="cycler" />
            <item index="8" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="9" class="java.lang.String" itemvalue="ogb" />
            <item index="10" class="java.lang.String" itemvalue="patsy" />
            <item index="11" class="java.lang.String" itemvalue="dgl-gpu" />
            <item index="12" class="java.lang.String" itemvalue="filelock" />
            <item index="13" class="java.lang.String" itemvalue="outdated" />
            <item index="14" class="java.lang.String" itemvalue="python-louvain" />
            <item index="15" class="java.lang.String" itemvalue="certifi" />
            <item index="16" class="java.lang.String" itemvalue="soupsieve" />
            <item index="17" class="java.lang.String" itemvalue="pyparsing" />
            <item index="18" class="java.lang.String" itemvalue="dgl-cpu" />
            <item index="19" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="20" class="java.lang.String" itemvalue="h5py" />
            <item index="21" class="java.lang.String" itemvalue="rdflib" />
            <item index="22" class="java.lang.String" itemvalue="torch-cluster" />
            <item index="23" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="24" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="25" class="java.lang.String" itemvalue="PySocks" />
            <item index="26" class="java.lang.String" itemvalue="matplotlib" />
            <item index="27" class="java.lang.String" itemvalue="googledrivedownloader" />
            <item index="28" class="java.lang.String" itemvalue="littleutils" />
            <item index="29" class="java.lang.String" itemvalue="idna" />
            <item index="30" class="java.lang.String" itemvalue="decorator" />
            <item index="31" class="java.lang.String" itemvalue="networkx" />
            <item index="32" class="java.lang.String" itemvalue="Pillow-SIMD" />
            <item index="33" class="java.lang.String" itemvalue="isodate" />
            <item index="34" class="java.lang.String" itemvalue="torch-sparse" />
            <item index="35" class="java.lang.String" itemvalue="llvmlite" />
            <item index="36" class="java.lang.String" itemvalue="numpy" />
            <item index="37" class="java.lang.String" itemvalue="requests" />
            <item index="38" class="java.lang.String" itemvalue="Jinja2" />
            <item index="39" class="java.lang.String" itemvalue="gdown" />
            <item index="40" class="java.lang.String" itemvalue="urllib3" />
            <item index="41" class="java.lang.String" itemvalue="torch-geometric" />
            <item index="42" class="java.lang.String" itemvalue="scipy" />
            <item index="43" class="java.lang.String" itemvalue="six" />
            <item index="44" class="java.lang.String" itemvalue="torch" />
            <item index="45" class="java.lang.String" itemvalue="chardet" />
            <item index="46" class="java.lang.String" itemvalue="pandas" />
            <item index="47" class="java.lang.String" itemvalue="tqdm" />
            <item index="48" class="java.lang.String" itemvalue="torch-spline-conv" />
            <item index="49" class="java.lang.String" itemvalue="ase" />
            <item index="50" class="java.lang.String" itemvalue="future" />
            <item index="51" class="java.lang.String" itemvalue="statsmodels" />
            <item index="52" class="java.lang.String" itemvalue="pytz" />
            <item index="53" class="java.lang.String" itemvalue="Pillow" />
            <item index="54" class="java.lang.String" itemvalue="torch_sparse" />
            <item index="55" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="56" class="java.lang.String" itemvalue="torch_geometric" />
            <item index="57" class="java.lang.String" itemvalue="tensorboard" />
            <item index="58" class="java.lang.String" itemvalue="jupyter" />
            <item index="59" class="java.lang.String" itemvalue="seaborn" />
            <item index="60" class="java.lang.String" itemvalue="opencv-python" />
            <item index="61" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="62" class="java.lang.String" itemvalue="torchvision" />
            <item index="63" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="64" class="java.lang.String" itemvalue="yacs" />
            <item index="65" class="java.lang.String" itemvalue="brotlipy" />
            <item index="66" class="java.lang.String" itemvalue="PyYAML" />
            <item index="67" class="java.lang.String" itemvalue="texttable" />
            <item index="68" class="java.lang.String" itemvalue="wheel" />
            <item index="69" class="java.lang.String" itemvalue="cffi" />
            <item index="70" class="java.lang.String" itemvalue="tzdata" />
            <item index="71" class="java.lang.String" itemvalue="cryptography" />
            <item index="72" class="java.lang.String" itemvalue="setuptools" />
            <item index="73" class="java.lang.String" itemvalue="mkl-random" />
            <item index="74" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="75" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="76" class="java.lang.String" itemvalue="pip" />
            <item index="77" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="78" class="java.lang.String" itemvalue="torchaudio" />
            <item index="79" class="java.lang.String" itemvalue="torch_scatter" />
            <item index="80" class="java.lang.String" itemvalue="dgl_cu101" />
            <item index="81" class="java.lang.String" itemvalue="marshmallow-sqlalchemy" />
            <item index="82" class="java.lang.String" itemvalue="validators" />
            <item index="83" class="java.lang.String" itemvalue="Flask_Reuploaded" />
            <item index="84" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="85" class="java.lang.String" itemvalue="Flask-Login" />
            <item index="86" class="java.lang.String" itemvalue="psutil" />
            <item index="87" class="java.lang.String" itemvalue="Flask-APScheduler" />
            <item index="88" class="java.lang.String" itemvalue="flask-marshmallow" />
            <item index="89" class="java.lang.String" itemvalue="Flask-Migrate" />
            <item index="90" class="java.lang.String" itemvalue="Flask-Mail" />
            <item index="91" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="92" class="java.lang.String" itemvalue="xgboost" />
            <item index="93" class="java.lang.String" itemvalue="Flask" />
            <item index="94" class="java.lang.String" itemvalue="lie_learn" />
            <item index="95" class="java.lang.String" itemvalue="dgl" />
            <item index="96" class="java.lang.String" itemvalue="packaging" />
            <item index="97" class="java.lang.String" itemvalue="MDAnalysis" />
            <item index="98" class="java.lang.String" itemvalue="graph_nets" />
            <item index="99" class="java.lang.String" itemvalue="MDAnalysisData" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>