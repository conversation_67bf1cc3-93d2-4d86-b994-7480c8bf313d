#!/usr/bin/env python3
"""
渐进式特征优化
从原始特征开始，逐步添加和优化特征
"""

import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader
from model_gte import MVGNN_GTE
from utils import TaskDataset
from edge_features import EdgeFeatures
from sklearn.metrics import roc_auc_score
from sklearn.preprocessing import StandardScaler
import subprocess
import time

def test_feature_configuration(config_name, use_golden_features, feature_processing=None):
    """测试特定的特征配置"""
    
    print(f"\n🧪 测试配置: {config_name}")
    print("-" * 40)
    
    # 构建训练命令
    cmd = [
        'python3', 'train_golden_robust.py',
        '--epochs', '10',
        '--batch_size', '8',
        '--hidden_dim', '128',
        '--num_encoder_layers', '2',
        '--learning_rate', '1e-3',
        '--folds', '2',
        '--simple_loss'
    ]
    
    # 如果不使用黄金特征，需要修改训练脚本
    if not use_golden_features:
        print("  使用原始特征")
    else:
        print("  使用黄金特征")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        end_time = time.time()
        
        if result.returncode == 0:
            # 解析结果
            output_lines = result.stdout.split('\n')
            mean_auc = None
            
            for line in output_lines:
                if 'Mean AUC:' in line:
                    try:
                        mean_auc = float(line.split('Mean AUC:')[1].split('±')[0].strip())
                        break
                    except:
                        pass
            
            print(f"  ✅ 成功 - AUC: {mean_auc:.4f}, 耗时: {(end_time-start_time)/60:.1f}分钟")
            return mean_auc
        else:
            print(f"  ❌ 失败: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print(f"  ⏰ 超时")
        return None
    except Exception as e:
        print(f"  💥 错误: {e}")
        return None

def create_improved_features():
    """创建改进的特征"""
    
    print("🔧 创建改进的特征")
    print("="*50)
    
    # 加载数据
    df = pd.read_csv('./datasets/PRO_Train335.csv')
    
    # 加载蛋白质特征
    protein_data = {}
    feature_path = './feature/'
    
    for pdb_id in df['ID'].unique():
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f"{pdb_id}_X.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_node_feature.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_mask.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_label.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_adj.tensor", weights_only=True)
            )
        except:
            continue
    
    print(f"✅ 加载了 {len(protein_data)} 个蛋白质")
    
    # 方案1：标准化的黄金特征
    print("\n🔧 方案1: 标准化黄金特征")
    
    all_golden_features = []
    pdb_ids = []
    
    # 收集所有黄金特征
    for pdb_id in protein_data.keys():
        golden_file = f'./feature_golden_fixed/{pdb_id}_golden_features.tensor'
        try:
            golden_features = torch.load(golden_file, weights_only=True)
            all_golden_features.append(golden_features)
            pdb_ids.append(pdb_id)
        except:
            continue
    
    if len(all_golden_features) > 0:
        # 合并所有特征进行标准化
        combined_features = torch.cat(all_golden_features, dim=0)
        
        # 标准化
        scaler = StandardScaler()
        normalized_features = scaler.fit_transform(combined_features.numpy())
        normalized_features = torch.tensor(normalized_features, dtype=torch.float32)
        
        # 分割回原来的蛋白质
        start_idx = 0
        for i, pdb_id in enumerate(pdb_ids):
            end_idx = start_idx + all_golden_features[i].shape[0]
            normalized_protein_features = normalized_features[start_idx:end_idx]
            
            # 保存标准化的特征
            output_file = f'./feature_golden_normalized/{pdb_id}_golden_features.tensor'
            torch.save(normalized_protein_features, output_file)
            
            start_idx = end_idx
        
        print(f"  ✅ 已保存 {len(pdb_ids)} 个标准化特征")
    
    # 方案2：选择性特征组合
    print("\n🔧 方案2: 选择性特征组合")
    
    for pdb_id in protein_data.keys():
        try:
            # 获取原始特征
            original_features = protein_data[pdb_id][1]  # node_features
            
            # 获取黄金特征
            golden_file = f'./feature_golden_fixed/{pdb_id}_golden_features.tensor'
            golden_features = torch.load(golden_file, weights_only=True)
            
            if golden_features.shape[1] == 1061:
                # 只使用ProtTrans部分 (前1024维)
                prottrans_features = golden_features[:, :1024]
                
                # 组合：原始特征 + ProtTrans
                if original_features.shape[0] == prottrans_features.shape[0]:
                    combined_features = torch.cat([original_features, prottrans_features], dim=1)
                    
                    # 保存组合特征
                    output_file = f'./feature_selective/{pdb_id}_selective_features.tensor'
                    torch.save(combined_features, output_file)
        
        except Exception as e:
            continue
    
    print("  ✅ 选择性特征组合完成")

def run_progressive_optimization():
    """运行渐进式优化"""
    
    print("🚀 渐进式特征优化")
    print("="*60)
    
    # 创建必要的目录
    import os
    os.makedirs('./feature_golden_normalized', exist_ok=True)
    os.makedirs('./feature_selective', exist_ok=True)
    
    # 创建改进的特征
    create_improved_features()
    
    # 测试不同配置
    results = {}
    
    # 配置1：原始特征（基线）
    print("\n" + "="*60)
    print("📊 配置对比测试")
    print("="*60)
    
    # 由于我们需要修改训练脚本来支持不同特征，
    # 这里提供一个简化的测试框架
    
    print("\n💡 建议的测试步骤:")
    print("1. 修改训练脚本支持原始特征")
    print("2. 测试原始特征性能（基线）")
    print("3. 测试标准化黄金特征")
    print("4. 测试选择性特征组合")
    print("5. 选择最佳配置")
    
    print("\n🔧 立即可执行的命令:")
    print("# 测试当前黄金特征")
    print("python3 train_golden_robust.py --epochs 15 --batch_size 8 --hidden_dim 128 --learning_rate 1e-3 --folds 3")
    
    print("\n# 如果要测试原始特征，需要:")
    print("1. 在utils.py中设置use_golden_features=False")
    print("2. 重新运行训练")

def create_original_feature_trainer():
    """创建使用原始特征的训练脚本"""
    
    trainer_code = '''#!/usr/bin/env python3
"""
使用原始特征的训练脚本
"""

import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader
from model_gte import MVGNN_GTE
from utils import TaskDataset
from edge_features import EdgeFeatures
from sklearn.metrics import roc_auc_score, precision_score, recall_score, f1_score, accuracy_score
from sklearn.model_selection import KFold
from tqdm import tqdm

def train_with_original_features():
    """使用原始特征训练"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    print("🌟 使用原始特征训练")
    
    # 加载数据
    df = pd.read_csv('./datasets/PRO_Train335.csv')
    
    # 加载蛋白质特征
    protein_data = {}
    feature_path = './feature/'
    
    for pdb_id in tqdm(df['ID'].unique(), desc="Loading protein data"):
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f"{pdb_id}_X.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_node_feature.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_mask.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_label.tensor", weights_only=True),
                torch.load(feature_path + f"{pdb_id}_adj.tensor", weights_only=True)
            )
        except:
            continue
    
    print(f"✅ 加载了 {len(protein_data)} 个蛋白质")
    
    # 过滤数据
    df = df[df['ID'].isin(protein_data.keys())]
    
    # 5折交叉验证
    kfold = KFold(n_splits=3, shuffle=True, random_state=42)
    fold_results = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(df)):
        print(f"\\n🔄 第 {fold+1} 折训练")
        
        train_df = df.iloc[train_idx].reset_index(drop=True)
        val_df = df.iloc[val_idx].reset_index(drop=True)
        
        # 创建数据集（使用原始特征）
        edge_features_module = EdgeFeatures(edge_features=16, top_k=30, augment_eps=0.0)
        
        train_dataset = TaskDataset(
            train_df, protein_data, 'label',
            use_graph_format=True,
            edge_features_module=edge_features_module,
            use_geometric_features=False,
            training=True,
            use_golden_features=False  # 关键：使用原始特征
        )
        
        val_dataset = TaskDataset(
            val_df, protein_data, 'label',
            use_graph_format=True,
            edge_features_module=edge_features_module,
            use_geometric_features=False,
            training=False,
            use_golden_features=False  # 关键：使用原始特征
        )
        
        train_dataloader = DataLoader(train_dataset, batch_size=8, shuffle=True, collate_fn=train_dataset.collate_fn)
        val_dataloader = DataLoader(val_dataset, batch_size=8, shuffle=False, collate_fn=val_dataset.collate_fn)
        
        # 创建模型（使用原始特征维度）
        model = MVGNN_GTE(
            node_features=1038,  # 原始特征维度
            edge_features=16,
            hidden_dim=128,
            num_encoder_layers=2,
            k_neighbors=30,
            augment_eps=0.0,
            dropout=0.1,
            num_heads=4,
            egnn_layers=1,
            use_coords_update=False,
            use_unet_gt=False,
            pooling_ratio=0.3,
            use_geometric_features=False,
            num_iterations=1,
            use_global_node=True
        ).to(device)
        
        # 训练配置
        criterion = torch.nn.BCEWithLogitsLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        
        best_val_auc = 0
        
        # 训练循环
        for epoch in range(15):
            model.train()
            train_preds = []
            train_labels = []
            
            for batch in train_dataloader:
                try:
                    if hasattr(batch, 'to'):
                        batch = batch.to(device)
                    
                    optimizer.zero_grad()
                    logits = model(batch=batch)
                    labels = batch.y.float().to(device)
                    
                    if hasattr(batch, 'real_nodes_mask') and batch.real_nodes_mask is not None:
                        real_mask = batch.real_nodes_mask
                        if real_mask.sum() < len(labels):
                            labels = labels[real_mask]
                    
                    loss = criterion(logits, labels)
                    loss.backward()
                    optimizer.step()
                    
                    probs = torch.sigmoid(logits)
                    train_preds.extend(probs.detach().cpu().numpy())
                    train_labels.extend(labels.detach().cpu().numpy())
                    
                except:
                    continue
            
            # 验证
            model.eval()
            val_preds = []
            val_labels = []
            
            with torch.no_grad():
                for batch in val_dataloader:
                    try:
                        if hasattr(batch, 'to'):
                            batch = batch.to(device)
                        
                        logits = model(batch=batch)
                        labels = batch.y.float().to(device)
                        
                        if hasattr(batch, 'real_nodes_mask') and batch.real_nodes_mask is not None:
                            real_mask = batch.real_nodes_mask
                            if real_mask.sum() < len(labels):
                                labels = labels[real_mask]
                        
                        probs = torch.sigmoid(logits)
                        val_preds.extend(probs.detach().cpu().numpy())
                        val_labels.extend(labels.detach().cpu().numpy())
                        
                    except:
                        continue
            
            # 计算AUC
            if len(train_preds) > 0 and len(np.unique(train_labels)) >= 2:
                train_auc = roc_auc_score(train_labels, train_preds)
            else:
                train_auc = 0.5
            
            if len(val_preds) > 0 and len(np.unique(val_labels)) >= 2:
                val_auc = roc_auc_score(val_labels, val_preds)
            else:
                val_auc = 0.5
            
            if val_auc > best_val_auc:
                best_val_auc = val_auc
            
            if epoch % 5 == 0:
                print(f"  Epoch {epoch+1}: Train AUC={train_auc:.4f}, Val AUC={val_auc:.4f}")
        
        fold_results.append(best_val_auc)
        print(f"✅ 第 {fold+1} 折完成，最佳AUC: {best_val_auc:.4f}")
    
    # 总结结果
    mean_auc = np.mean(fold_results)
    std_auc = np.std(fold_results)
    
    print(f"\\n🏆 原始特征训练结果:")
    print(f"平均AUC: {mean_auc:.4f} ± {std_auc:.4f}")
    print(f"各折AUC: {fold_results}")

if __name__ == "__main__":
    train_with_original_features()
'''
    
    with open('train_original_features.py', 'w') as f:
        f.write(trainer_code)
    
    print("✅ 已创建原始特征训练脚本: train_original_features.py")

def main():
    """主函数"""
    
    print("🎯 黄金特征性能问题解决方案")
    print("="*60)
    
    # 创建原始特征训练脚本
    create_original_feature_trainer()
    
    # 运行渐进式优化
    run_progressive_optimization()
    
    print("\\n🚀 下一步行动:")
    print("1. 运行原始特征训练: python3 train_original_features.py")
    print("2. 对比性能差异")
    print("3. 根据结果选择最佳特征配置")

if __name__ == "__main__":
    main()
'''

    with open('progressive_feature_optimization.py', 'w') as f:
        f.write(trainer_code)
    
    print("✅ 已创建渐进式优化脚本")

if __name__ == "__main__":
    main()
