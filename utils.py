# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import os, time, random
import datetime
from tqdm import tqdm
from sklearn.metrics import auc, roc_auc_score, precision_recall_curve
from sklearn import metrics
from torch.utils.data import DataLoader, RandomSampler
from focalLoss import *
from noam_opt import *
import torch

# 🚀 添加图数据支持
try:
    from torch_geometric.data import Data
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    print("⚠️ torch_geometric not available, using fallback mode")
    TORCH_GEOMETRIC_AVAILABLE = False

# 🚀 【管理员】引入torch_scatter库 - 解决批处理难题的关键工具
try:
    from torch_scatter import scatter_mean, scatter_add, scatter
    TORCH_SCATTER_AVAILABLE = True
    print("✅ torch_scatter 可用 - 全局节点批处理已就绪")
except ImportError:
    print("⚠️ torch_scatter 不可用，将使用fallback聚合方法")
    TORCH_SCATTER_AVAILABLE = False

    # 创建fallback scatter函数
    def scatter_mean(src, index, dim=0, dim_size=None):
        """Fallback scatter_mean implementation"""
        if dim_size is None:
            dim_size = index.max().item() + 1

        # 创建输出张量
        out_shape = list(src.shape)
        out_shape[dim] = dim_size
        out = torch.zeros(out_shape, dtype=src.dtype, device=src.device)
        count = torch.zeros(dim_size, dtype=torch.long, device=src.device)

        # 手动聚合
        for i in range(src.size(dim)):
            idx = index[i].item()
            if dim == 0:
                out[idx] += src[i]
            else:
                out[:, idx] += src[:, i]
            count[idx] += 1

        # 计算平均值
        count = count.clamp(min=1).float()
        if dim == 0:
            out = out / count.unsqueeze(-1)
        else:
            out = out / count.unsqueeze(0).unsqueeze(-1)

        return out

    # 🚀 创建简单的Data类作为fallback
    class Data:
        def __init__(self, x=None, pos=None, edge_index=None, edge_attr=None, y=None, **kwargs):
            self.x = x
            self.pos = pos
            self.edge_index = edge_index
            self.edge_attr = edge_attr
            self.y = y
            for key, value in kwargs.items():
                setattr(self, key, value)

        def cuda(self):
            """将所有张量移动到GPU"""
            for attr_name in dir(self):
                if not attr_name.startswith('_'):
                    attr_value = getattr(self, attr_name)
                    if isinstance(attr_value, torch.Tensor):
                        setattr(self, attr_name, attr_value.cuda())
            return self

        def clone(self):
            """克隆Data对象"""
            cloned = Data()
            for attr_name in dir(self):
                if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                    attr_value = getattr(self, attr_name)
                    if isinstance(attr_value, torch.Tensor):
                        setattr(cloned, attr_name, attr_value.clone())
                    else:
                        setattr(cloned, attr_name, attr_value)
            return cloned


def vectorized_knn_to_edge_index(E_idx, mask, device='cpu'):
    """
    🚀 高效的矢量化k-NN到edge_index转换

    Args:
        E_idx: k-NN索引 [L, K]
        mask: 有效节点掩码 [L]
        device: 设备

    Returns:
        edge_index: [2, num_edges]
        valid_edge_mask: 有效边的掩码
    """
    L, K = E_idx.shape

    # 只处理有效节点
    valid_nodes = torch.where(mask)[0]
    num_valid = len(valid_nodes)

    if num_valid == 0:
        return torch.empty((2, 0), dtype=torch.long, device=device), torch.empty(0, dtype=torch.bool, device=device)

    # 创建源节点索引
    src_nodes = valid_nodes.unsqueeze(1).expand(-1, K).flatten()  # [num_valid * K]

    # 获取目标节点索引
    tgt_nodes = E_idx[valid_nodes].flatten()  # [num_valid * K]

    # 🔧 修复：安全地创建有效边掩码，处理超出范围的索引
    valid_edge_mask = torch.zeros(len(tgt_nodes), dtype=torch.bool, device=device)
    valid_indices = (tgt_nodes >= 0) & (tgt_nodes < len(mask))
    valid_edge_mask[valid_indices] = mask[tgt_nodes[valid_indices]]

    # 过滤掉无效边
    valid_edges = valid_edge_mask.nonzero().squeeze(-1)

    if len(valid_edges) == 0:
        return torch.empty((2, 0), dtype=torch.long, device=device), torch.empty(0, dtype=torch.bool, device=device)

    edge_index = torch.stack([
        src_nodes[valid_edges],
        tgt_nodes[valid_edges]
    ], dim=0).to(device)

    return edge_index, valid_edge_mask


def prepare_edge_features_fast(E, E_idx, mask, device='cpu'):
    """
    🚀 高效的边特征准备 - 与vectorized_knn_to_edge_index完全一致

    Args:
        E: 边特征 [L, K, edge_dim]
        E_idx: k-NN索引 [L, K]
        mask: 有效节点掩码 [L]
        device: 设备

    Returns:
        edge_attr: 边特征 [num_edges, edge_dim]
    """
    L, K, edge_dim = E.shape

    # 只处理有效节点
    valid_nodes = torch.where(mask)[0]
    num_valid = len(valid_nodes)

    if num_valid == 0:
        return torch.empty((0, edge_dim), dtype=E.dtype, device=device)

    # 🚀 【关键修复】使用与vectorized_knn_to_edge_index完全相同的逻辑

    # 1. 获取有效节点的边特征，展平为 [num_valid * K, edge_dim]
    valid_E = E[valid_nodes]  # [num_valid, K, edge_dim]
    edge_features_flat = valid_E.reshape(-1, edge_dim)  # [num_valid * K, edge_dim]

    # 2. 获取目标节点索引，展平为 [num_valid * K]
    tgt_nodes = E_idx[valid_nodes].flatten()  # [num_valid * K]

    # 3. 创建有效边掩码（与vectorized_knn_to_edge_index完全相同的逻辑）
    valid_edge_mask = torch.zeros(len(tgt_nodes), dtype=torch.bool, device=device)
    valid_indices = (tgt_nodes >= 0) & (tgt_nodes < len(mask))
    valid_edge_mask[valid_indices] = mask[tgt_nodes[valid_indices]]

    # 4. 过滤掉无效边的特征
    valid_edges = valid_edge_mask.nonzero().squeeze(-1)

    if len(valid_edges) == 0:
        return torch.empty((0, edge_dim), dtype=E.dtype, device=device)

    return edge_features_flat[valid_edges].to(device)


def Seed_everything(seed=2024):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True


def Metric(preds, labels,best_threshold = None):
    labels = np.array(labels).reshape(-1)
    preds = np.array(preds).reshape(-1)

    # 🔧 关键修复：检查标签中是否同时包含0和1
    unique_labels = np.unique(labels)
    # print(f"🔍 Metric函数内部: unique_labels={unique_labels}, 数量={len(unique_labels)}")  # 🔧 关闭调试输出

    if len(unique_labels) < 2:
        # 如果标签只有一类（例如，全是0或全是1），则无法计算AUC等指标
        # 我们返回一个默认的、无意义的值，比如0.5表示随机猜测
        print(f"⚠️ Warning: Only one class present in labels: {unique_labels}. AUC and other metrics are not well-defined.")
        # 返回一个包含默认值的元组，确保返回值的数量与正常流程一致
        # (AUC, AUPRC, mcc, binary_acc, precision, recall, f1)
        return 0.5, 0.0, 0.0, 0.5, 0.0, 0.0, 0.0

    # 🔧 修复：显式转换标签为整数类型，避免 scikit-learn 的类型混合错误
    labels = labels.astype(int)
    if best_threshold == None:
        best_f1 = 0
        best_threshold = 0
        for threshold in range(0, 100):
            threshold = threshold / 100
            binary_pred = [1 if pred >= threshold else 0 for pred in preds]
            # 🔧 安全的标签二值化处理
            try:
                binary_true = [1 if int(label) > 0 else 0 for label in labels]
                f1 = metrics.f1_score(binary_true, binary_pred, average='binary')
                if f1 > best_f1:
                    best_f1 = f1
                    best_threshold = threshold
            except (ValueError, TypeError) as e:
                print(f"⚠️ Warning: Error in threshold calculation: {e}")
                continue
    binary_pred = [1 if pred >= best_threshold else 0 for pred in preds]
    # 🔧 安全的标签二值化处理
    binary_true = [1 if int(label) > 0 else 0 for label in labels]
    binary_acc = metrics.accuracy_score(binary_true, binary_pred)
    # 🔧 修复：为 precision 和 recall 添加 zero_division 参数以避免警告
    precision = metrics.precision_score(binary_true, binary_pred, average='binary', zero_division=0)
    recall = metrics.recall_score(binary_true, binary_pred, average='binary', zero_division=0)
    f1 = metrics.f1_score(binary_true, binary_pred, average='binary', zero_division=0)
    mcc = metrics.matthews_corrcoef(binary_true, binary_pred)
    # 🔧 修复：直接使用原始标签计算AUC，避免二次检查的bug
    try:
        # 🚀 关键修复：直接使用已经验证过的标签（前面已经检查过类别数量）
        AUC = roc_auc_score(labels, preds)
        precisions, recalls, _ = precision_recall_curve(labels, preds)
        AUPRC = auc(recalls, precisions)
    except ValueError as e:
        print(f"⚠️ Warning: AUC calculation failed: {e}. Setting AUC to 0.5")
        AUC = 0.5
        AUPRC = 0.5
    return AUC, AUPRC, mcc ,binary_acc,precision,recall,f1


def Write_log(logFile, text, isPrint=True):
    if isPrint:
        print(text)
    logFile.write(text)
    logFile.write('\n')
    return None


class TaskDataset:
    def __init__(self, df, protein_data, label_name, use_graph_format=True, edge_features_module=None, use_geometric_features=False, training=True, use_golden_features=False):
        self.df = df
        self.protein_data = protein_data
        self.label_name = label_name
        # 🚀 允许在没有torch_geometric的情况下也使用图格式
        self.use_graph_format = use_graph_format
        self.edge_features_module = edge_features_module
        self.use_geometric_features = use_geometric_features
        self.training = training  # 🚀 训练模式标志，用于数据增强
        self.use_golden_features = use_golden_features  # 🌟 新增：是否使用黄金特征

        # 加载预计算的几何特征
        if use_geometric_features:
            try:
                import pickle
                with open('geometric_features/geometric_features.pkl', 'rb') as f:
                    self.geometric_features = pickle.load(f)
                print(f"✅ Loaded geometric features for {len(self.geometric_features)} proteins")
            except Exception as e:
                print(f"⚠️ Failed to load geometric features: {e}")
                self.geometric_features = {}
        else:
            self.geometric_features = {}

        # 🌟 加载黄金特征 (修复版)
        if use_golden_features:
            self.golden_features = {}
            try:
                import os
                golden_dir = './feature_golden_fixed'  # 🔧 使用修复版目录
                if os.path.exists(golden_dir):
                    for file in os.listdir(golden_dir):
                        if file.endswith('_golden_features.tensor'):
                            protein_id = file.replace('_golden_features.tensor', '')
                            feature_path = os.path.join(golden_dir, file)
                            self.golden_features[protein_id] = torch.load(feature_path, weights_only=True)
                    print(f"✅ Loaded golden features for {len(self.golden_features)} proteins")
                else:
                    print(f"⚠️ Golden features directory not found: {golden_dir}")
            except Exception as e:
                print(f"⚠️ Failed to load golden features: {e}")
                self.golden_features = {}
        else:
            self.golden_features = {}

        # 🔧 检测特征维度
        self.base_feature_dim = self._detect_feature_dim()
        print(f"🔧 检测到特征维度: {self.base_feature_dim} ({'ESM2' if self.base_feature_dim == 1294 else 'T5' if self.base_feature_dim == 1038 else '未知'})")

        print(f"🔍 Debug: use_graph_format={self.use_graph_format}, edge_features_module={self.edge_features_module is not None}")
        if self.use_graph_format:
            print("🚀 Using optimized graph format for faster training")
        else:
            print("📊 Using standard format")

    def _detect_feature_dim(self):
        """动态检测特征维度，支持T5和ESM2"""
        try:
            # 获取第一个蛋白质的特征来检测维度
            first_protein_id = self.df.iloc[0]['ID']
            if first_protein_id in self.protein_data:
                _, protein_node_features, _, _, _ = self.protein_data[first_protein_id]
                detected_dim = protein_node_features.shape[1]
                return detected_dim
            else:
                print("⚠️ 无法检测特征维度，使用默认T5维度")
                return 1038  # 默认T5维度
        except Exception as e:
            print(f"⚠️ 特征维度检测失败: {e}，使用默认T5维度")
            return 1038

    def __len__(self):
        return (self.df.shape[0])

    def __getitem__(self, idx):
        pdb_id = self.df.loc[idx, 'ID']
        protein_X, protein_node_features, protein_masks, labels, adj = self.protein_data[pdb_id]

        if self.use_graph_format and self.edge_features_module is not None:
            # 🚀 高效图格式：预处理成图数据
            try:
                # 只处理有效节点
                valid_mask = protein_masks.bool()
                num_valid = valid_mask.sum().item()

                if num_valid == 0:
                    # 处理空蛋白质的情况 - 返回空的Data对象，确保维度一致
                    if self.use_golden_features:
                        expected_dim = 1061  # 黄金特征维度
                    else:
                        # 🔧 动态检测特征维度：支持T5(1038)和ESM2(1294)
                        expected_dim = self.base_feature_dim + (3 if self.use_geometric_features else 0)
                    return Data(
                        x=torch.empty((0, expected_dim)),  # 🔧 使用一致的维度
                        pos=torch.empty((0, 3)),
                        edge_index=torch.empty((2, 0), dtype=torch.long),
                        edge_attr=torch.empty((0, 16)),  # 假设边特征维度是16
                        y=torch.empty(0),
                        has_global_node=torch.tensor(False),  # 🚀 标记是否有全局节点
                        num_real_nodes=torch.tensor(0),
                        real_nodes_mask=torch.empty(0, dtype=torch.bool)  # 🔧 空的节点掩码
                    )

                # 获取有效节点的数据
                valid_X = protein_X[valid_mask]  # [num_valid, 3]

                # 🌟 使用黄金特征或原始特征
                if self.use_golden_features and pdb_id in self.golden_features:
                    # 使用预计算的黄金特征
                    golden_features = self.golden_features[pdb_id]  # [seq_len, 1061]
                    if len(golden_features) == len(protein_node_features):
                        valid_features = golden_features[valid_mask]  # [num_valid, 1061]
                        # 🔧 完全移除详细输出，保持训练日志简洁
                        # if hasattr(self, 'debug_mode') and self.debug_mode:
                        #     print(f"✅ Using golden features for {pdb_id}: {valid_features.shape}")
                    else:
                        print(f"⚠️ Golden features length mismatch for {pdb_id}: {len(golden_features)} vs {len(protein_node_features)}")
                        valid_features = protein_node_features[valid_mask]  # [num_valid, node_dim]
                else:
                    valid_features = protein_node_features[valid_mask]  # [num_valid, node_dim]

                valid_labels = labels[valid_mask]  # [num_valid]

                # � 暂时注释几何特征增强，避免维度问题
                # TODO: 在维度问题解决后重新启用
                # 🚀 使用预计算的几何特征（强化一致性保证）
                if self.use_geometric_features:
                    try:
                        if pdb_id in self.geometric_features:
                            # 获取预计算的几何特征
                            precomputed_geometric = self.geometric_features[pdb_id]  # numpy array
                            precomputed_geometric = torch.tensor(precomputed_geometric, dtype=torch.float32)

                            # 应用mask获取有效的几何特征
                            valid_geometric = precomputed_geometric[valid_mask]  # [num_valid, 3]

                            # 确保维度正确
                            if valid_geometric.size(0) != valid_X.size(0):
                                print(f"⚠️ Geometric feature size mismatch for {pdb_id}: {valid_geometric.size(0)} vs {valid_X.size(0)}")
                                valid_geometric = torch.zeros(valid_X.size(0), 3)
                        else:
                            print(f"⚠️ No precomputed geometric features for {pdb_id}")
                            valid_geometric = torch.zeros(valid_X.size(0), 3)

                        # 拼接几何特征到原始特征
                        valid_features = torch.cat([valid_features, valid_geometric], dim=1)

                    except Exception as e:
                        print(f"⚠️ Failed to use geometric features for {pdb_id}: {e}")
                        # 失败时添加零特征确保维度一致
                        zero_geometric = torch.zeros(valid_X.size(0), 3)
                        valid_features = torch.cat([valid_features, zero_geometric], dim=1)
                        # print(f"✅ Added zero geometric features to {pdb_id}: {valid_features.size(1)} dims")  # 🔧 注释掉调试输出
                else:
                    # 🔧 如果不使用几何特征，但模型期望1041维，添加零特征
                    if hasattr(self, 'use_geometric_features') and self.use_geometric_features:
                        print(f"⚠️ Geometric features disabled but expected, adding zeros for {pdb_id}")
                        zero_geometric = torch.zeros(valid_X.size(0), 3)
                        valid_features = torch.cat([valid_features, zero_geometric], dim=1)

                # 🔧 最终维度检查和强制修正
                if self.use_golden_features:
                    expected_dim = 1061  # 黄金特征维度
                else:
                    expected_dim = self.base_feature_dim + (3 if self.use_geometric_features else 0)

                if valid_features.size(1) != expected_dim:
                    # print(f"⚠️ Feature dimension mismatch for {pdb_id}: got {valid_features.size(1)}, expected {expected_dim}")  # 🔧 注释掉调试输出
                    # 强制修正维度
                    if valid_features.size(1) < expected_dim:
                        padding = torch.zeros(valid_features.size(0), expected_dim - valid_features.size(1))
                        valid_features = torch.cat([valid_features, padding], dim=1)
                        # print(f"✅ Padded {pdb_id} to {valid_features.size(1)} dimensions")  # 🔧 注释掉调试输出
                    elif valid_features.size(1) > expected_dim:
                        valid_features = valid_features[:, :expected_dim]
                        # print(f"✅ Truncated {pdb_id} to {valid_features.size(1)} dimensions")  # 🔧 注释掉调试输出

                # 使用边特征模块计算k-NN和边特征
                with torch.no_grad():
                    # 重新整形为批次格式以使用EdgeFeatures模块
                    X_batch = valid_X.unsqueeze(0)  # [1, num_valid, 3]
                    mask_batch = torch.ones(1, num_valid, dtype=torch.bool)  # [1, num_valid]

                    # 🔧 安全的EdgeFeatures调用，添加错误处理
                    try:
                        # EdgeFeatures.forward 返回 (E, E_idx)
                        result = self.edge_features_module(X_batch, mask_batch)
                        if result is None or len(result) != 2:
                            raise ValueError("EdgeFeatures returned invalid result")

                        E, E_idx = result
                        if E is None or E_idx is None:
                            raise ValueError("EdgeFeatures returned None values")

                        E_idx = E_idx.squeeze(0)  # [num_valid, K]
                        E = E.squeeze(0)  # [num_valid, K, edge_dim]

                    except Exception as e:
                        print(f"⚠️ EdgeFeatures failed for {pdb_id}: {e}")
                        # 创建默认的边特征
                        K = 30  # k_neighbors
                        edge_dim = 16  # 默认边特征维度
                        E_idx = torch.zeros(num_valid, K, dtype=torch.long)
                        E = torch.zeros(num_valid, K, edge_dim, dtype=torch.float32)

                # 🔧 安全的图格式转换
                try:
                    edge_index, _ = vectorized_knn_to_edge_index(E_idx, torch.ones(num_valid, dtype=torch.bool))
                    edge_attr = prepare_edge_features_fast(E, E_idx, torch.ones(num_valid, dtype=torch.bool))

                    # 验证转换结果
                    if edge_index is None or edge_attr is None:
                        raise ValueError("Graph conversion returned None values")

                except Exception as e:
                    print(f"⚠️ Graph conversion failed for {pdb_id}: {e}")
                    # 创建最小的图结构
                    edge_index = torch.empty((2, 0), dtype=torch.long)
                    edge_attr = torch.empty((0, 16), dtype=torch.float32)

                # 🚀 数据增强（仅在训练时应用）
                if hasattr(self, 'training') and self.training:
                    # 1. 坐标噪声增强
                    noise_scale = 0.1  # 0.1 Å的噪声
                    coord_noise = torch.randn_like(valid_X) * noise_scale
                    valid_X = valid_X + coord_noise

                    # 2. 特征噪声增强
                    feature_noise_scale = 0.02
                    feature_noise = torch.randn_like(valid_features) * feature_noise_scale
                    valid_features = valid_features + feature_noise

                    # 3. 特征dropout增强
                    if torch.rand(1).item() < 0.1:  # 10%概率应用特征dropout
                        dropout_mask = torch.rand(valid_features.size(1)) > 0.05  # 保留95%的特征
                        valid_features = valid_features * dropout_mask.float()

                # 🔧 图数据创建前的最终维度验证
                if self.use_golden_features:
                    expected_dim = 1061  # 黄金特征维度
                else:
                    expected_dim = self.base_feature_dim + (3 if self.use_geometric_features else 0)

                if valid_features.size(1) != expected_dim:
                    # print(f"🚨 CRITICAL: {pdb_id} still has wrong dimension {valid_features.size(1)}, forcing to {expected_dim}")  # 🔧 注释掉调试输出
                    if valid_features.size(1) < expected_dim:
                        padding = torch.zeros(valid_features.size(0), expected_dim - valid_features.size(1))
                        valid_features = torch.cat([valid_features, padding], dim=1)
                    else:
                        valid_features = valid_features[:, :expected_dim]

                # 🚀 全局上下文节点机制：添加虚拟全局节点
                if num_valid > 0:  # 只有当存在有效节点时才添加全局节点
                    # 1. 为全局节点创建特征（使用零向量作为占位符，后续在模型中替换为可学习嵌入）
                    global_node_feature = torch.zeros(1, valid_features.size(1))
                    x_with_global = torch.cat([valid_features, global_node_feature], dim=0)  # [num_valid+1, feature_dim]

                    # 2. 为全局节点创建坐标（使用所有真实节点坐标的均值）
                    global_node_pos = torch.mean(valid_X, dim=0, keepdim=True)  # [1, 3]
                    pos_with_global = torch.cat([valid_X, global_node_pos], dim=0)  # [num_valid+1, 3]

                    # 3. 为全局节点创建标签（使用-1标记，表示不参与损失计算）
                    global_node_label = torch.tensor([-1.0])  # 特殊标记
                    y_with_global = torch.cat([valid_labels, global_node_label], dim=0)  # [num_valid+1]

                    # 4. 添加全局节点相关的边
                    global_node_idx = num_valid  # 全局节点的索引

                    # 创建从真实节点到全局节点的边
                    real_to_global_edges = torch.stack([
                        torch.arange(num_valid),
                        torch.full((num_valid,), global_node_idx)
                    ], dim=0)  # [2, num_valid]

                    # 创建从全局节点到真实节点的边
                    global_to_real_edges = torch.stack([
                        torch.full((num_valid,), global_node_idx),
                        torch.arange(num_valid)
                    ], dim=0)  # [2, num_valid]

                    # 合并所有边
                    edge_index_with_global = torch.cat([
                        edge_index, real_to_global_edges, global_to_real_edges
                    ], dim=1)  # [2, num_edges + 2*num_valid]

                    # 5. 为新边创建边特征（使用特殊的全局边特征）
                    global_edge_dim = edge_attr.size(1) if edge_attr.size(0) > 0 else 16
                    global_edge_features = torch.zeros(2 * num_valid, global_edge_dim)  # 全局边使用零特征

                    edge_attr_with_global = torch.cat([
                        edge_attr, global_edge_features
                    ], dim=0)  # [num_edges + 2*num_valid, edge_dim]

                    # 使用包含全局节点的数据
                    final_x = x_with_global
                    final_pos = pos_with_global
                    final_edge_index = edge_index_with_global
                    final_edge_attr = edge_attr_with_global
                    final_y = y_with_global
                    has_global = True
                else:
                    # 如果没有有效节点，不添加全局节点
                    final_x = valid_features
                    final_pos = valid_X
                    final_edge_index = edge_index
                    final_edge_attr = edge_attr
                    final_y = valid_labels
                    has_global = False

                # 🔧 创建real_nodes_mask以支持批处理
                if has_global:
                    # 真实节点 + 全局节点
                    real_nodes_mask = torch.cat([
                        torch.ones(num_valid, dtype=torch.bool),  # 真实节点
                        torch.zeros(1, dtype=torch.bool)          # 全局节点
                    ])
                else:
                    # 只有真实节点
                    real_nodes_mask = torch.ones(num_valid, dtype=torch.bool)

                # 🔧 最终的None值检查
                if final_x is None or final_pos is None or final_edge_index is None or final_edge_attr is None or final_y is None:
                    print(f"⚠️ None values detected in final data for {pdb_id}, creating safe defaults")
                    if self.use_golden_features:
                        expected_dim = 1061
                    else:
                        expected_dim = self.base_feature_dim + (3 if self.use_geometric_features else 0)

                    return Data(
                        x=torch.zeros((1, expected_dim)),
                        pos=torch.zeros((1, 3)),
                        edge_index=torch.empty((2, 0), dtype=torch.long),
                        edge_attr=torch.empty((0, 16)),
                        y=torch.zeros(1),
                        has_global_node=torch.tensor(False),
                        num_real_nodes=torch.tensor(1),
                        real_nodes_mask=torch.ones(1, dtype=torch.bool)
                    )

                return Data(
                    x=final_x,
                    pos=final_pos,
                    edge_index=final_edge_index,
                    edge_attr=final_edge_attr,
                    y=final_y,
                    has_global_node=torch.tensor(has_global),  # 🚀 标记是否包含全局节点
                    num_real_nodes=torch.tensor(num_valid),    # 🚀 记录真实节点数量
                    real_nodes_mask=real_nodes_mask             # 🔧 支持批处理的节点掩码
                )

            except Exception as e:
                print(f"⚠️ Graph format failed for {pdb_id}: {e}, creating empty Data object")
                # 🔧 关键修复：即使失败也返回Data对象，确保维度一致
                if self.use_golden_features:
                    expected_dim = 1061  # 黄金特征维度
                else:
                    expected_dim = self.base_feature_dim + (3 if self.use_geometric_features else 0)
                return Data(
                    x=torch.empty((0, expected_dim)),  # 🔧 使用一致的维度
                    pos=torch.empty((0, 3)),
                    edge_index=torch.empty((2, 0), dtype=torch.long),
                    edge_attr=torch.empty((0, 16)),
                    y=torch.empty(0),
                    has_global_node=torch.tensor(False),  # 🚀 标记没有全局节点
                    num_real_nodes=torch.tensor(0),       # 🚀 记录真实节点数量为0
                    real_nodes_mask=torch.empty(0, dtype=torch.bool)  # 🔧 空的节点掩码
                )

        # 🔧 标准格式：处理黄金特征或几何特征后返回元组格式
        if self.use_golden_features and pdb_id in self.golden_features:
            # 🌟 使用预计算的黄金特征
            golden_features = self.golden_features[pdb_id]  # [seq_len, 1061]
            if len(golden_features) == len(protein_node_features):
                protein_node_features = golden_features
                # 🔧 完全移除详细输出，保持训练日志简洁
                # if hasattr(self, 'debug_mode') and self.debug_mode:
                #     print(f"✅ Using golden features for {pdb_id} (standard format): {protein_node_features.shape}")
            else:
                print(f"⚠️ Golden features length mismatch for {pdb_id}: {len(golden_features)} vs {len(protein_node_features)}")
        elif self.use_geometric_features:
            try:
                if pdb_id in self.geometric_features:
                    # 获取预计算的几何特征
                    precomputed_geometric = self.geometric_features[pdb_id]  # numpy array
                    precomputed_geometric = torch.tensor(precomputed_geometric, dtype=torch.float32)

                    # 拼接几何特征到原始特征
                    protein_node_features = torch.cat([protein_node_features, precomputed_geometric], dim=1)
                else:
                    zero_geometric = torch.zeros(protein_node_features.size(0), 3)
                    protein_node_features = torch.cat([protein_node_features, zero_geometric], dim=1)
            except Exception as e:
                print(f"⚠️ Failed to use geometric features for {pdb_id}: {e}")
                # 失败时添加零特征确保维度一致
                zero_geometric = torch.zeros(protein_node_features.size(0), 3)
                protein_node_features = torch.cat([protein_node_features, zero_geometric], dim=1)
                # print(f"✅ Added zero geometric features to {pdb_id} (standard format): {protein_node_features.size(1)} dims")  # 🔧 注释掉调试输出

        return pdb_id, protein_X, protein_node_features, protein_masks, labels, adj

    def collate_fn(self, batch):
        """自定义collate函数，处理不同格式的数据"""
        if len(batch) == 0:
            return batch

        # 检查第一个元素的类型
        first_item = batch[0]

        if hasattr(first_item, 'x'):  # 图格式 (Data对象)
            try:
                from torch_geometric.data import Batch
                return Batch.from_data_list(batch)
            except ImportError:
                print("⚠️ torch_geometric Batch not available, using simple batching")
                # 🚀 创建简单的批处理机制
                return self._simple_batch_from_data_list(batch)

        elif isinstance(first_item, tuple) and len(first_item) == 6:  # 标准格式元组
            # 标准格式: (pdb_id, protein_X, protein_node_features, protein_masks, labels, adj)
            pdb_ids = [item[0] for item in batch]

            # 找到最大序列长度用于padding
            max_len = max(item[2].size(0) for item in batch)  # protein_node_features的长度

            batch_size = len(batch)
            node_dim = batch[0][2].size(1)  # 节点特征维度

            # 初始化批次张量
            protein_X = torch.zeros(batch_size, max_len, 3)
            protein_node_features = torch.zeros(batch_size, max_len, node_dim)
            protein_masks = torch.zeros(batch_size, max_len, dtype=torch.bool)
            labels = torch.zeros(batch_size, max_len)
            adj = torch.zeros(batch_size, max_len, max_len)

            # 填充数据
            for i, (pdb_id, X, node_feat, mask, label, adjacency) in enumerate(batch):
                seq_len = X.size(0)
                protein_X[i, :seq_len] = X
                protein_node_features[i, :seq_len] = node_feat
                protein_masks[i, :seq_len] = mask
                labels[i, :seq_len] = label
                adj[i, :seq_len, :seq_len] = adjacency

            return pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj

        else:
            # 未知格式，返回原始batch
            return batch

    def _simple_batch_from_data_list(self, data_list):
        """简单的批处理机制，用于没有torch_geometric的情况"""
        if len(data_list) == 0:
            return Data()

        # 合并所有节点特征
        x_list = [data.x for data in data_list if data.x is not None]
        pos_list = [data.pos for data in data_list if data.pos is not None]
        y_list = [data.y for data in data_list if data.y is not None]

        if not x_list:
            return Data()

        # 拼接节点特征和标签
        batch_x = torch.cat(x_list, dim=0)
        batch_pos = torch.cat(pos_list, dim=0) if pos_list else None
        batch_y = torch.cat(y_list, dim=0) if y_list else None

        # 处理边信息 - 合并所有边索引
        edge_index_list = []
        edge_attr_list = []
        node_offset = 0

        for data in data_list:
            if hasattr(data, 'edge_index') and data.edge_index is not None:
                # 调整边索引以适应批处理
                adjusted_edge_index = data.edge_index + node_offset
                edge_index_list.append(adjusted_edge_index)

                if hasattr(data, 'edge_attr') and data.edge_attr is not None:
                    edge_attr_list.append(data.edge_attr)

            # 更新节点偏移量
            if data.x is not None:
                node_offset += data.x.size(0)

        # 合并边信息
        if edge_index_list:
            batch_edge_index = torch.cat(edge_index_list, dim=1)
            batch_edge_attr = torch.cat(edge_attr_list, dim=0) if edge_attr_list else None
        else:
            # 如果没有边信息，创建空的边张量
            batch_edge_index = torch.empty((2, 0), dtype=torch.long)
            batch_edge_attr = None

        # 创建批次信息
        batch_info = []
        for i, data in enumerate(data_list):
            if data.x is not None:
                batch_info.extend([i] * data.x.size(0))
        batch_info = torch.tensor(batch_info, dtype=torch.long)

        # 创建简单的批处理对象
        batched_data = Data(
            x=batch_x,
            pos=batch_pos,
            y=batch_y,
            edge_index=batch_edge_index,
            edge_attr=batch_edge_attr,
            batch=batch_info
        )

        # 🔧 修复：正确处理全局节点属性的批处理
        if hasattr(data_list[0], 'has_global_node'):
            # 确保正确提取标量值
            has_global_values = []
            for data in data_list:
                has_global = getattr(data, 'has_global_node', False)
                if torch.is_tensor(has_global):
                    has_global_values.append(has_global.item())  # 提取标量值
                else:
                    has_global_values.append(bool(has_global))
            batched_data.has_global_node = torch.tensor(has_global_values)

        if hasattr(data_list[0], 'num_real_nodes'):
            # 确保正确提取标量值
            num_real_values = []
            for data in data_list:
                num_real = getattr(data, 'num_real_nodes', data.x.size(0) if data.x is not None else 0)
                if torch.is_tensor(num_real):
                    num_real_values.append(num_real.item())  # 提取标量值
                else:
                    num_real_values.append(int(num_real))
            batched_data.num_real_nodes = torch.tensor(num_real_values)

        return batched_data


# main function
def model_test( test, protein_data, model_class, config, logit=False, output_root='./output/', args=None):
    label_name = ['label']  # some task may have mutiple labels
    sequence_name = "sequence"
    gpus = [0]
    print("Available GPUs", gpus)

    output_result = output_root + "prediction/"
    output_weight = output_root + "weight/"
    if not os.path.exists(output_result):
        os.mkdir(output_result)


    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config['hidden_dim']
    num_encoder_layers = config['num_encoder_layers']
    k_neighbors = config['k_neighbors']
    augment_eps = config['augment_eps']
    dropout = config['dropout']
    id_name = config['id_name']
    batch_size = config['batch_size']
    folds = config['folds']


    if test is not None:

        log = open(output_result + 'test.log', 'w', buffering=1)
        Write_log(log, str(config) + '\n')
        sub = test[[id_name, sequence_name]]

        if isinstance(label_name, list):
            for l in label_name:
                sub[l] = 0.0
                sub[l] = sub[l].astype(np.float32)
        else:
            sub[label_name] = 0.0

        test_dataset = TaskDataset(test, protein_data, label_name)
        test_dataloader = DataLoader(test_dataset, batch_size=batch_size, collate_fn=test_dataset.collate_fn,
                                     shuffle=False, drop_last=False, num_workers=args.num_workers, prefetch_factor=2)
        models = []
        for fold in range(folds):
            if not os.path.exists(output_weight + 'fold%s.ckpt' % fold):
                print("not exist train model")
                continue

            model = model_class(node_features, edge_features, hidden_dim, num_encoder_layers, k_neighbors, augment_eps, dropout)
            model.cuda()
            state_dict = torch.load(output_weight + 'fold%s.ckpt' % fold, torch.device('cuda'))
            model.load_state_dict(state_dict)
            if len(gpus) > 1:
                model = nn.DataParallel(model, device_ids=gpus, output_device=gpus[0])

            model.eval()
            models.append(model)
        print('model count:', len(models))

        test_preds = []
        test_outputs = []
        test_Y = []
        all_protein_node_features = []
        all_labels = []
        with torch.no_grad():
            for data in tqdm(test_dataloader):
                protein_X, protein_node_features, protein_masks, y, adj = [d.cuda() for d in data[1:]]
                all_protein_node_features.append(protein_node_features.detach().cpu().numpy())
                all_labels.append(y.detach().cpu().numpy())
                if logit:
                    outputs = [model(protein_X, protein_node_features, protein_masks, adj).sigmoid() for model in models]
                else:
                    outputs = [model(protein_X, protein_node_features, protein_masks) for model in models]

                outputs = torch.stack(outputs, 0).mean(0)  # 5个模型预测结果求平均,最终shape=(bsize, max_len)
                test_outputs.append(outputs.detach().cpu().numpy())

                test_seq_y = torch.masked_select(y, protein_masks.bool())
                test_seq_preds = torch.masked_select(outputs, protein_masks.bool())

                test_preds.append(test_seq_preds.cpu().detach().numpy())
                test_Y.append(test_seq_y.cpu().detach().numpy())


        test_preds = np.concatenate(test_preds)
        test_Y = np.concatenate(test_Y)
        test_metric = Metric(test_preds, test_Y)
        Write_log(log,'test_auc:%.6f, test_auprc:%.6f, testFYT_mccL:%.6f, test_acc:%.6f, test_pre:%.6f, test_rec:%.6f, test_f1:%.6f' \
                      % (test_metric[0], test_metric[1], test_metric[2], test_metric[3],
                         test_metric[4], test_metric[5], test_metric[6]))

        test_outputs = np.concatenate(test_outputs)  # shape = (num_samples, max_len) or (num_samples,  4 * max_len)


        sub['label'] = sub['label'].astype(object)
        for i in range(len(sub)):
            sub.at[i, 'label'] = test_outputs[i, :len(sub.loc[i, sequence_name])].tolist()
        sub.to_csv(output_result + 'result.csv', index=False)
        log.close()

