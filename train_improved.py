#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的MVGNN-PPIS训练脚本
实施性能突破的关键改进策略
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import argparse
from pathlib import Path
from sklearn.model_selection import KFold
from sklearn.metrics import roc_auc_score, precision_recall_curve, auc
import warnings
warnings.filterwarnings('ignore')

# 导入模型和工具
from model_gte import MVGNN_GTE
from utils import *
from improved_config import IMPROVED_CONFIG

def create_improved_model(config):
    """创建改进的模型"""
    model = MVGNN_GTE(
        node_features=config['node_features'],
        edge_features=config['edge_features'], 
        hidden_dim=config['hidden_dim'],
        num_encoder_layers=config['num_encoder_layers'],
        k_neighbors=config['k_neighbors'],
        augment_eps=config['augment_eps'],
        dropout=config['dropout'],
        use_unet_gt=config['use_unet_gt'],
        pooling_ratio=config['pooling_ratio'],
        use_graph_collapse=config['use_graph_collapse'],
        use_geometric_features=config['use_geometric_features'],
        num_iterations=config['num_iterations'],
        use_global_node=config['use_global_node']
    )
    return model

def improved_focal_loss(outputs, targets, alpha=0.25, gamma=2.0):
    """改进的Focal Loss"""
    bce_loss = nn.BCEWithLogitsLoss(reduction='none')(outputs, targets.float())
    pt = torch.exp(-bce_loss)
    focal_loss = alpha * (1-pt)**gamma * bce_loss
    return focal_loss.mean()

def improved_combined_loss(outputs, targets):
    """改进的组合损失函数"""
    focal = improved_focal_loss(outputs, targets, alpha=0.3, gamma=2.5)
    bce = nn.BCEWithLogitsLoss()(outputs, targets.float())
    return 0.6 * focal + 0.4 * bce

def train_with_gradient_accumulation(model, dataloader, optimizer, criterion, 
                                   accumulation_steps=2, max_grad_norm=0.5, device='cuda'):
    """使用梯度累积的训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    optimizer.zero_grad()
    
    for i, batch in enumerate(dataloader):
        # 数据移动到设备
        if hasattr(batch, 'to'):
            batch = batch.to(device)
        else:
            # 处理标准格式数据
            for key in batch:
                if torch.is_tensor(batch[key]):
                    batch[key] = batch[key].to(device)
        
        # 前向传播
        try:
            outputs = model(batch)
            if hasattr(batch, 'y'):
                targets = batch.y
            else:
                targets = batch['labels']
            
            loss = criterion(outputs, targets)
            
            # 损失归一化
            loss = loss / accumulation_steps
            loss.backward()
            
            # 收集预测和标签
            with torch.no_grad():
                preds = torch.sigmoid(outputs).cpu().numpy()
                labels = targets.cpu().numpy()
                all_preds.extend(preds.flatten())
                all_labels.extend(labels.flatten())
            
            # 梯度累积
            if (i + 1) % accumulation_steps == 0:
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
                optimizer.step()
                optimizer.zero_grad()
            
            total_loss += loss.item() * accumulation_steps
            
        except Exception as e:
            print(f"训练批次 {i} 出错: {e}")
            continue
    
    # 计算指标
    avg_loss = total_loss / len(dataloader)
    auc_score = roc_auc_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.5
    
    return avg_loss, auc_score

def validate_model(model, dataloader, criterion, device='cuda'):
    """验证模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch in dataloader:
            try:
                # 数据移动到设备
                if hasattr(batch, 'to'):
                    batch = batch.to(device)
                else:
                    for key in batch:
                        if torch.is_tensor(batch[key]):
                            batch[key] = batch[key].to(device)
                
                outputs = model(batch)
                if hasattr(batch, 'y'):
                    targets = batch.y
                else:
                    targets = batch['labels']
                
                loss = criterion(outputs, targets)
                total_loss += loss.item()
                
                # 收集预测和标签
                preds = torch.sigmoid(outputs).cpu().numpy()
                labels = targets.cpu().numpy()
                all_preds.extend(preds.flatten())
                all_labels.extend(labels.flatten())
                
            except Exception as e:
                print(f"验证批次出错: {e}")
                continue
    
    avg_loss = total_loss / len(dataloader)
    auc_score = roc_auc_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.5
    
    return avg_loss, auc_score, all_preds, all_labels

def validate_data_consistency(train_loader, val_loader):
    """验证数据一致性"""
    print("🔍 数据一致性检查:")
    
    try:
        # 收集训练和验证数据的统计信息
        train_labels = []
        val_labels = []
        
        for batch in train_loader:
            if hasattr(batch, 'y'):
                train_labels.extend(batch.y.cpu().numpy())
            else:
                train_labels.extend(batch['labels'].cpu().numpy())
        
        for batch in val_loader:
            if hasattr(batch, 'y'):
                val_labels.extend(batch.y.cpu().numpy())
            else:
                val_labels.extend(batch['labels'].cpu().numpy())
        
        train_pos_ratio = np.mean(train_labels)
        val_pos_ratio = np.mean(val_labels)
        
        print(f"  训练集正样本比例: {train_pos_ratio:.4f}")
        print(f"  验证集正样本比例: {val_pos_ratio:.4f}")
        print(f"  比例差异: {abs(train_pos_ratio - val_pos_ratio):.4f}")
        
        is_consistent = abs(train_pos_ratio - val_pos_ratio) < 0.1
        print(f"  数据一致性: {'✅ 通过' if is_consistent else '❌ 不一致'}")
        
        return is_consistent
        
    except Exception as e:
        print(f"  数据一致性检查失败: {e}")
        return False

def train_single_fold(fold, train_loader, val_loader, config, device='cuda'):
    """训练单个fold"""
    print(f"\n🚀 开始训练 Fold {fold}")
    print("-" * 50)
    
    # 数据一致性检查
    is_consistent = validate_data_consistency(train_loader, val_loader)
    if not is_consistent:
        print("⚠️ 警告: 数据分布不一致，可能影响性能")
    
    # 创建模型
    model = create_improved_model(config).to(device)
    
    # 优化器和调度器
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, 
        verbose=True, min_lr=1e-6
    )
    
    criterion = improved_combined_loss
    
    # 训练循环
    best_val_auc = 0
    patience_counter = 0
    
    for epoch in range(config['epochs']):
        # 训练
        train_loss, train_auc = train_with_gradient_accumulation(
            model, train_loader, optimizer, criterion,
            accumulation_steps=config['gradient_accumulation_steps'],
            max_grad_norm=config['max_grad_norm'],
            device=device
        )
        
        # 验证
        val_loss, val_auc, val_preds, val_labels = validate_model(
            model, val_loader, criterion, device
        )
        
        # 学习率调度
        scheduler.step(val_auc)
        
        print(f"Epoch {epoch+1}/{config['epochs']} - "
              f"Train Loss: {train_loss:.4f}, Train AUC: {train_auc:.4f}, "
              f"Val Loss: {val_loss:.4f}, Val AUC: {val_auc:.4f}")
        
        # 早停检查
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'improved_model_fold{fold}.pth')
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                print(f"早停触发，最佳验证AUC: {best_val_auc:.4f}")
                break
    
    return best_val_auc

def main():
    """主训练函数"""
    print("🚀 MVGNN-PPIS 改进训练开始")
    print("=" * 60)
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载配置
    config = IMPROVED_CONFIG
    print(f"使用改进配置，关键参数:")
    print(f"  学习率: {config['learning_rate']}")
    print(f"  隐藏维度: {config['hidden_dim']}")
    print(f"  编码器层数: {config['num_encoder_layers']}")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  Dropout: {config['dropout']}")
    
    # 这里需要根据实际情况加载数据
    # 由于我们需要切换到图格式，这部分需要您根据现有的数据加载代码进行调整
    print("\n⚠️ 注意: 请确保数据加载使用图格式 (use_graph_format=True)")
    print("建议下一步: 修改现有的train_optimal.py，应用这些改进配置")

if __name__ == "__main__":
    main()
