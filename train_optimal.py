#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
from tqdm import tqdm
import argparse
from model_gte import MVGNN_GTE
from utils import Seed_everything, Metric, Write_log, TaskDataset
from focalLoss import FocalLoss
from noam_opt import get_std_opt

warnings.simplefilter('ignore')

def validate_data_consistency(train_df, val_df):
    """🔍 验证数据分布一致性 - 诊断性能瓶颈的关键步骤"""
    train_pos_ratio = train_df['label'].apply(lambda x: x.count('1') / len(x)).mean()
    val_pos_ratio = val_df['label'].apply(lambda x: x.count('1') / len(x)).mean()

    print(f"  训练集正样本比例: {train_pos_ratio:.4f}")
    print(f"  验证集正样本比例: {val_pos_ratio:.4f}")
    print(f"  比例差异: {abs(train_pos_ratio - val_pos_ratio):.4f}")

    is_consistent = abs(train_pos_ratio - val_pos_ratio) < 0.1
    print(f"  数据一致性: {'✅ 通过' if is_consistent else '❌ 不一致'}")

    return is_consistent, train_pos_ratio, val_pos_ratio

def train_model(train_df, val_df, protein_data, model_class, config, fold, output_root='./output/', args=None):
    """
    🚀 改进的模型训练函数 - 基于学习曲线诊断的性能突破
    """
    print(f"\n🚀 开始训练 Fold {fold} (性能突破版)")
    print("-" * 50)

    # 🔍 关键改进1: 数据一致性检查
    print("🔍 数据一致性检查:")
    is_consistent, train_pos_ratio, val_pos_ratio = validate_data_consistency(train_df, val_df)
    if not is_consistent:
        print("⚠️ 警告: 数据分布不一致，这可能是性能瓶颈的根本原因！")

    # Setup paths and logging
    output_weight = output_root + "weight/"
    if not os.path.exists(output_weight):
        os.makedirs(output_weight)

    log = open(output_weight + f'fold{fold}.log', 'w', buffering=1)
    Write_log(log, str(config) + '\n')
    Write_log(log, f"数据一致性检查: {is_consistent}\n")
    Write_log(log, f"训练集正样本比例: {train_pos_ratio:.4f}\n")
    Write_log(log, f"验证集正样本比例: {val_pos_ratio:.4f}\n")
    
    # Extract configuration parameters
    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config['hidden_dim']
    num_encoder_layers = config['num_encoder_layers']
    k_neighbors = config['k_neighbors']
    augment_eps = config.get('augment_eps', 0.0)
    dropout = config['dropout']
    batch_size = config['batch_size']
    epochs = config['epochs']
    patience = config['patience']
    
    # Reset DataFrame indices to ensure continuous indexing
    train_df_reset = train_df.reset_index(drop=True)
    val_df_reset = val_df.reset_index(drop=True)

    # 🚀 关键改进2: 强制使用图格式 - 解决数据格式瓶颈
    use_graph_format = config.get('use_graph_format', True)
    print(f"📊 使用图数据格式: {use_graph_format}")

    # Create datasets and dataloaders
    train_dataset = TaskDataset(train_df_reset, protein_data, 'label',
                               use_graph_format=use_graph_format, use_geometric_features=True,
                               training=True, use_golden_features=False,
                               k_neighbors=k_neighbors, augment_eps=augment_eps)
    val_dataset = TaskDataset(val_df_reset, protein_data, 'label',
                             use_graph_format=use_graph_format, use_geometric_features=True,
                             training=False, use_golden_features=False,
                             k_neighbors=k_neighbors, augment_eps=0.0)

    train_dataloader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=train_dataset.collate_fn,
        num_workers=0,
        drop_last=False
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False,
        num_workers=0
    )
    
    # 🏆 Initialize model with FINAL_BEST_CONFIG
    model = model_class(
        node_features=node_features,
        edge_features=edge_features,
        hidden_dim=hidden_dim,  # 96 - 最优隐藏层维度
        num_encoder_layers=num_encoder_layers,  # 4 - 最优编码器层数
        k_neighbors=k_neighbors,
        augment_eps=augment_eps,
        dropout=dropout,  # 0.3 - 最优dropout
        num_heads=4,
        egnn_layers=2,  # 增强几何建模
        use_coords_update=config.get('use_coords_update', True),  # 几何等变性
        use_unet_gt=True,  # 🏆 FINAL_BEST_CONFIG关键组件
        pooling_ratio=config.get('pooling_ratio', 0.3),  # 保守池化策略
        use_graph_collapse=config.get('use_graph_collapse', True),  # 图折叠机制
        use_geometric_features=True,
        num_iterations=config.get('num_iterations', 1),  # 平衡性能和稳定性
        use_global_node=True
    )
    # Use only single GPU (GPU 0)
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    print(f"Using device: {device}")
    
    # 🚀 关键改进3: 优化损失函数和优化器
    pos_weight = torch.tensor([config.get('class_weight_ratio', 5.4)]).to(device)
    bce_loss = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    focal_loss = FocalLoss(alpha=config.get('focal_alpha', 0.3),      # 0.75→0.3 减少极端关注
                          gamma=config.get('focal_gamma', 2.5))       # 3.0→2.5 平衡难易样本

    def improved_combined_loss(outputs, targets):
        focal = focal_loss(outputs, targets)
        bce = bce_loss(outputs, targets)
        return 0.6 * focal + 0.4 * bce  # 调整权重比例

    # 🚀 关键改进4: 增强优化器配置
    learning_rate = config.get('learning_rate', 2e-4)  # 降低学习率提高稳定性
    weight_decay = config.get('weight_decay', 1e-4)    # 增加权重衰减

    optimizer = torch.optim.AdamW(model.parameters(),
                                 lr=learning_rate,
                                 weight_decay=weight_decay)

    print(f"📋 优化器配置: lr={learning_rate}, weight_decay={weight_decay}")

    # 🚀 关键改进5: 更稳定的学习率调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5,
        verbose=True, min_lr=1e-6
    )

    # 梯度累积和裁剪参数
    accumulation_steps = config.get('gradient_accumulation_steps', 2)
    max_grad_norm = config.get('max_grad_norm', 0.5)
    print(f"📋 训练策略: 梯度累积步数={accumulation_steps}, 梯度裁剪阈值={max_grad_norm}")
    
    # Training loop
    best_val_auc = 0
    best_epoch = 0
    no_improvement = 0
    
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        train_bar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{epochs} [Train]")
        for batch in train_bar:
            if isinstance(batch, tuple) and len(batch) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
                
                # Move to device
                protein_X = protein_X.to(device)
                protein_node_features = protein_node_features.to(device)
                protein_masks = protein_masks.to(device)
                labels = labels.to(device)
                adj = adj.to(device)
                
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj)
                
                # Only calculate loss on valid positions
                valid_mask = protein_masks.bool()
                valid_outputs = outputs[valid_mask]
                valid_labels = labels[valid_mask]
                
                if len(valid_outputs) > 0:
                    loss = improved_combined_loss(valid_outputs, valid_labels)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    train_loss += loss.item()
                    
                    # Collect predictions and labels
                    probs = torch.sigmoid(valid_outputs)
                    train_preds.append(probs.detach().cpu().numpy())
                    train_labels.append(valid_labels.detach().cpu().numpy())
                    
                    train_bar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        # Validation phase
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            val_bar = tqdm(val_dataloader, desc=f"Epoch {epoch+1}/{epochs} [Val]")
            for batch in val_bar:
                if isinstance(batch, tuple) and len(batch) == 6:
                    pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
                    
                    # Move to device
                    protein_X = protein_X.to(device)
                    protein_node_features = protein_node_features.to(device)
                    protein_masks = protein_masks.to(device)
                    labels = labels.to(device)
                    adj = adj.to(device)
                    
                    outputs = model(protein_X, protein_node_features, protein_masks, adj)
                    
                    # Only calculate loss on valid positions
                    valid_mask = protein_masks.bool()
                    valid_outputs = outputs[valid_mask]
                    valid_labels = labels[valid_mask]
                    
                    if len(valid_outputs) > 0:
                        loss = improved_combined_loss(valid_outputs, valid_labels)
                        val_loss += loss.item()
                        
                        # Collect predictions and labels
                        probs = torch.sigmoid(valid_outputs)
                        val_preds.append(probs.cpu().numpy())
                        val_labels.append(valid_labels.cpu().numpy())
        
        # Calculate metrics
        if len(train_preds) > 0 and len(val_preds) > 0:
            train_preds = np.concatenate([p.flatten() for p in train_preds])
            train_labels = np.concatenate([l.flatten() for l in train_labels])
            val_preds = np.concatenate([p.flatten() for p in val_preds])
            val_labels = np.concatenate([l.flatten() for l in val_labels])
            
            train_metrics = Metric(train_preds, train_labels)
            val_metrics = Metric(val_preds, val_labels)
            
            # Log metrics
            log_message = (f"Epoch {epoch+1}/{epochs} - "
                          f"Train Loss: {train_loss/len(train_dataloader):.6f}, "
                          f"Val Loss: {val_loss/len(val_dataloader):.6f}, "
                          f"Train AUC: {train_metrics[0]:.6f}, "
                          f"Val AUC: {val_metrics[0]:.6f}, "
                          f"Train AUPRC: {train_metrics[1]:.6f}, "
                          f"Val AUPRC: {val_metrics[1]:.6f}, "
                          f"Train MCC: {train_metrics[2]:.6f}, "
                          f"Val MCC: {val_metrics[2]:.6f}, "
                          f"Train Acc: {train_metrics[3]:.6f}, "
                          f"Val Acc: {val_metrics[3]:.6f}, "
                          f"Train Precision: {train_metrics[4]:.6f}, "
                          f"Val Precision: {val_metrics[4]:.6f}, "
                          f"Train Recall: {train_metrics[5]:.6f}, "
                          f"Val Recall: {val_metrics[5]:.6f}, "
                          f"Train F1: {train_metrics[6]:.6f}, "
                          f"Val F1: {val_metrics[6]:.6f}")
            
            Write_log(log, log_message)
            
            # 🚀 关键改进6: 改进的学习率调度和早停策略
            current_val_auc = val_metrics[0]  # Using AUC as the primary metric

            # 学习率调度 - 基于验证AUC
            scheduler.step(current_val_auc)
            current_lr = optimizer.param_groups[0]['lr']

            if current_val_auc > best_val_auc:
                best_val_auc = current_val_auc
                best_epoch = epoch
                no_improvement = 0

                # Save the best model with更多信息
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'best_val_auc': best_val_auc,
                    'config': config,
                    'data_consistent': is_consistent
                }, output_weight + f'fold{fold}.ckpt')

                Write_log(log, f"🎯 新的最佳模型! Epoch {epoch+1}, AUC: {best_val_auc:.6f}, LR: {current_lr:.2e}")
            else:
                no_improvement += 1

            # 记录学习率变化
            Write_log(log, f"学习率: {current_lr:.2e}, 无改进轮数: {no_improvement}/{patience}")

            # Early stopping
            if no_improvement >= patience:
                Write_log(log, f"Early stopping at epoch {epoch+1}. Best epoch: {best_epoch+1} with validation AUC: {best_val_auc:.6f}")
                break

        # 🚀 更新学习率调度器
        scheduler.step()

    log.close()
    return best_val_auc


def main():
    parser = argparse.ArgumentParser(description='MVGNN-GTE Training')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    # 🏆 FINAL_BEST_CONFIG - 经过验证的最优配置
    parser.add_argument('--epochs', type=int, default=25, help='Number of epochs - 关键突破')
    parser.add_argument('--folds', type=int, default=5, help='Number of folds for cross-validation')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate - 关键突破')
    parser.add_argument('--hidden_dim', type=int, default=96, help='Hidden dimension')
    parser.add_argument('--num_encoder_layers', type=int, default=4, help='Number of encoder layers')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout rate')
    parser.add_argument('--patience', type=int, default=8, help='Early stopping patience')
    parser.add_argument('--focal_alpha', type=float, default=0.75, help='Focal loss alpha')
    parser.add_argument('--focal_gamma', type=float, default=3.0, help='Focal loss gamma')
    parser.add_argument('--class_weight_ratio', type=float, default=5.4, help='Class weight ratio')
    parser.add_argument('--output_root', default='./output/', help='Output directory')
    parser.add_argument('--num_workers', type=int, default=0, help='Number of workers for data loading')

    # 🏆 FINAL_BEST_CONFIG扩展参数 - 平衡性能和稳定性
    parser.add_argument('--use_coords_update', action='store_true', default=False, help='Enable coordinate updates (可选)')
    parser.add_argument('--pooling_ratio', type=float, default=0.3, help='Pooling ratio - 保守策略')
    parser.add_argument('--use_graph_collapse', action='store_true', default=True, help='Enable graph collapse mechanism')
    parser.add_argument('--num_iterations', type=int, default=1, help='Number of iterations - 平衡性能')

    args = parser.parse_args()

    # Set random seed
    Seed_everything(args.seed)

    # Load data
    df = pd.read_csv('./datasets/PRO_Train335.csv')
    print(f"Loaded dataset with {len(df)} samples")

    # Load protein data
    protein_data = {}
    feature_path = './feature/'

    print("Loading protein features...")
    for pdb_id in tqdm(df['ID'].unique()):
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f'{pdb_id}_X.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_node_feature.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_mask.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_label.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_adj.tensor', weights_only=True)
            )
        except FileNotFoundError:
            continue

    print(f"Successfully loaded {len(protein_data)} protein features")

    # Filter dataframe to only include proteins with features
    df = df[df['ID'].isin(protein_data.keys())].reset_index(drop=True)
    print(f"Filtered dataset size: {len(df)}")

    # 🚀 BREAKTHROUGH_CONFIG - 基于学习曲线诊断的性能突破配置
    # 核心改进: 解决数据不一致性和训练不稳定问题
    config = {
        'node_features': 1297,  # ESM2 (1280) + DSSP (14) + geometric (3)
        'edge_features': 16,
        'hidden_dim': getattr(args, 'hidden_dim', 128),        # 96→128 增强模型容量
        'num_encoder_layers': getattr(args, 'num_encoder_layers', 5),  # 4→5 增加深度
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': getattr(args, 'dropout', 0.4),              # 0.3→0.4 增强正则化
        'batch_size': getattr(args, 'batch_size', 8),          # 4→8 减少梯度噪声
        'epochs': getattr(args, 'epochs', 40),                 # 25→40 更充分训练
        'patience': getattr(args, 'patience', 12),             # 8→12 避免过早停止
        'learning_rate': getattr(args, 'learning_rate', 2e-4), # 0.001→2e-4 提高稳定性
        'weight_decay': getattr(args, 'weight_decay', 1e-4),   # 新增权重衰减
        'focal_alpha': getattr(args, 'focal_alpha', 0.3),      # 0.75→0.3 调整focal权重
        'focal_gamma': getattr(args, 'focal_gamma', 2.5),      # 3.0→2.5 减少极端关注
        'class_weight_ratio': getattr(args, 'class_weight_ratio', 5.4),
        # 🚀 关键改进 - 训练稳定性增强
        'use_graph_format': getattr(args, 'use_graph_format', True),  # 强制图格式
        'gradient_accumulation_steps': getattr(args, 'gradient_accumulation_steps', 2),
        'max_grad_norm': getattr(args, 'max_grad_norm', 0.5),  # 梯度裁剪
        'use_coords_update': getattr(args, 'use_coords_update', False),
        'pooling_ratio': getattr(args, 'pooling_ratio', 0.3),
        'use_graph_collapse': getattr(args, 'use_graph_collapse', True),
        'num_iterations': getattr(args, 'num_iterations', 1)
    }

    print("Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # Cross-validation
    kfold = KFold(n_splits=args.folds, shuffle=True, random_state=args.seed)
    fold_aucs = []

    for fold, (train_idx, val_idx) in enumerate(kfold.split(df)):
        print(f"\n{'='*60}")
        print(f"Training Fold {fold+1}/{args.folds}")
        print(f"{'='*60}")

        train_df_fold = df.iloc[train_idx].reset_index(drop=True)
        val_df_fold = df.iloc[val_idx].reset_index(drop=True)

        # Filter out samples without features
        train_df_fold = train_df_fold[train_df_fold['ID'].isin(protein_data.keys())].reset_index(drop=True)
        val_df_fold = val_df_fold[val_df_fold['ID'].isin(protein_data.keys())].reset_index(drop=True)

        print(f"Train samples: {len(train_df_fold)}")
        print(f"Validation samples: {len(val_df_fold)}")

        # Train model
        fold_auc = train_model(
            train_df_fold, val_df_fold, protein_data,
            MVGNN_GTE, config, fold+1, args.output_root, args
        )

        fold_aucs.append(fold_auc)
        print(f"Fold {fold+1} AUC: {fold_auc:.4f}")

    # 🚀 性能突破结果分析
    mean_auc = np.mean(fold_aucs)
    std_auc = np.std(fold_aucs)
    min_auc = np.min(fold_aucs)
    max_auc = np.max(fold_aucs)

    print(f"\n{'='*60}")
    print("🚀 MVGNN-PPIS 性能突破结果")
    print(f"{'='*60}")
    print("基于学习曲线诊断的系统性改进效果:")
    print(f"{'='*60}")

    for i, auc in enumerate(fold_aucs):
        status = "🎯 优秀" if auc > 0.80 else "✅ 良好" if auc > 0.70 else "⚠️ 需改进" if auc > 0.60 else "❌ 失效"
        print(f"Fold {i+1}: AUC = {auc:.4f} {status}")

    print(f"\n📊 统计结果:")
    print(f"  平均AUC: {mean_auc:.4f} ± {std_auc:.4f}")
    print(f"  最佳AUC: {max_auc:.4f}")
    print(f"  最差AUC: {min_auc:.4f}")
    print(f"  AUC范围: {max_auc - min_auc:.4f}")

    # 性能突破评估
    print(f"\n🎯 性能突破评估:")
    if mean_auc > 0.75:
        print("🎉 重大突破! 平均AUC > 0.75")
    elif std_auc < 0.05:
        print("✅ 稳定性显著改善! AUC标准差 < 0.05")
    elif min_auc > 0.60:
        print("📈 基础性能提升! 所有fold AUC > 0.60")
    else:
        print("📋 仍有改进空间，建议进一步优化")

    # 与之前结果对比提示
    print(f"\n💡 改进效果对比:")
    print(f"  之前平均AUC: 0.6706 ± 0.1396")
    print(f"  当前平均AUC: {mean_auc:.4f} ± {std_auc:.4f}")
    improvement = mean_auc - 0.6706
    stability_improvement = 0.1396 - std_auc
    print(f"  性能提升: {improvement:+.4f}")
    print(f"  稳定性提升: {stability_improvement:+.4f}")

    if improvement > 0.05:
        print("🚀 性能显著提升!")
    if stability_improvement > 0.05:
        print("🎯 稳定性显著改善!")

    # Save results
    results_file = os.path.join(args.output_root, 'cross_validation_results.txt')
    with open(results_file, 'w') as f:
        f.write("Cross-Validation Results\n")
        f.write("="*60 + "\n")
        for i, auc in enumerate(fold_aucs):
            f.write(f"Fold {i+1}: AUC = {auc:.4f}\n")
        f.write(f"\nMean AUC: {mean_auc:.4f} ± {std_auc:.4f}\n")
        f.write(f"Best AUC: {max(fold_aucs):.4f}\n")
        f.write(f"\nConfiguration:\n")
        for key, value in config.items():
            f.write(f"  {key}: {value}\n")


if __name__ == "__main__":
    main()
