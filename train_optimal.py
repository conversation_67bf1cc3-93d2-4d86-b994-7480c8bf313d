#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
from tqdm import tqdm
import argparse
from model_gte import MVGNN_GTE
from utils import Seed_everything, Metric, Write_log, TaskDataset
from focalLoss import FocalLoss
from noam_opt import get_std_opt

warnings.simplefilter('ignore')

def train_model(train_df, val_df, protein_data, model_class, config, fold, output_root='./output/', args=None):
    """
    Train a model for a single fold
    """
    # Setup paths and logging
    output_weight = output_root + "weight/"
    if not os.path.exists(output_weight):
        os.makedirs(output_weight)
    
    log = open(output_weight + f'fold{fold}.log', 'w', buffering=1)
    Write_log(log, str(config) + '\n')
    
    # Extract configuration parameters
    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config['hidden_dim']
    num_encoder_layers = config['num_encoder_layers']
    k_neighbors = config['k_neighbors']
    augment_eps = config.get('augment_eps', 0.0)
    dropout = config['dropout']
    batch_size = config['batch_size']
    epochs = config['epochs']
    patience = config['patience']
    
    # Reset DataFrame indices to ensure continuous indexing
    train_df_reset = train_df.reset_index(drop=True)
    val_df_reset = val_df.reset_index(drop=True)

    # Create datasets and dataloaders
    train_dataset = TaskDataset(train_df_reset, protein_data, 'label', 
                               use_graph_format=False, use_geometric_features=True, 
                               training=True, use_golden_features=False)
    val_dataset = TaskDataset(val_df_reset, protein_data, 'label',
                             use_graph_format=False, use_geometric_features=True,
                             training=False, use_golden_features=False)

    train_dataloader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=train_dataset.collate_fn,
        num_workers=0,
        drop_last=False
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False,
        num_workers=0
    )
    
    # 🏆 Initialize model with FINAL_BEST_CONFIG
    model = model_class(
        node_features=node_features,
        edge_features=edge_features,
        hidden_dim=hidden_dim,  # 96 - 最优隐藏层维度
        num_encoder_layers=num_encoder_layers,  # 4 - 最优编码器层数
        k_neighbors=k_neighbors,
        augment_eps=augment_eps,
        dropout=dropout,  # 0.3 - 最优dropout
        num_heads=4,
        egnn_layers=2,  # 增强几何建模
        use_coords_update=config.get('use_coords_update', True),  # 几何等变性
        use_unet_gt=True,  # 🏆 FINAL_BEST_CONFIG关键组件
        pooling_ratio=config.get('pooling_ratio', 0.3),  # 保守池化策略
        use_graph_collapse=config.get('use_graph_collapse', True),  # 图折叠机制
        use_geometric_features=True,
        num_iterations=config.get('num_iterations', 1),  # 平衡性能和稳定性
        use_global_node=True
    )
    # Use only single GPU (GPU 0)
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    print(f"Using device: {device}")
    
    # Initialize loss function and optimizer
    pos_weight = torch.tensor([config.get('class_weight_ratio', 5.4)]).to(device)
    bce_loss = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    focal_loss = FocalLoss(alpha=config.get('focal_alpha', 0.75),
                          gamma=config.get('focal_gamma', 3.0))
    
    def combined_loss(outputs, targets):
        focal = focal_loss(outputs, targets)
        bce = bce_loss(outputs, targets)
        return 0.7 * focal + 0.3 * bce
    
    optimizer = torch.optim.AdamW(model.parameters(),
                                 lr=config.get('learning_rate', 0.001),
                                 weight_decay=1e-5)

    # 🚀 改进: 高级学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # Training loop
    best_val_auc = 0
    best_epoch = 0
    no_improvement = 0
    
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        train_bar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{epochs} [Train]")
        for batch in train_bar:
            if isinstance(batch, tuple) and len(batch) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
                
                # Move to device
                protein_X = protein_X.to(device)
                protein_node_features = protein_node_features.to(device)
                protein_masks = protein_masks.to(device)
                labels = labels.to(device)
                adj = adj.to(device)
                
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj)
                
                # Only calculate loss on valid positions
                valid_mask = protein_masks.bool()
                valid_outputs = outputs[valid_mask]
                valid_labels = labels[valid_mask]
                
                if len(valid_outputs) > 0:
                    loss = combined_loss(valid_outputs, valid_labels)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    train_loss += loss.item()
                    
                    # Collect predictions and labels
                    probs = torch.sigmoid(valid_outputs)
                    train_preds.append(probs.detach().cpu().numpy())
                    train_labels.append(valid_labels.detach().cpu().numpy())
                    
                    train_bar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        # Validation phase
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            val_bar = tqdm(val_dataloader, desc=f"Epoch {epoch+1}/{epochs} [Val]")
            for batch in val_bar:
                if isinstance(batch, tuple) and len(batch) == 6:
                    pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = batch
                    
                    # Move to device
                    protein_X = protein_X.to(device)
                    protein_node_features = protein_node_features.to(device)
                    protein_masks = protein_masks.to(device)
                    labels = labels.to(device)
                    adj = adj.to(device)
                    
                    outputs = model(protein_X, protein_node_features, protein_masks, adj)
                    
                    # Only calculate loss on valid positions
                    valid_mask = protein_masks.bool()
                    valid_outputs = outputs[valid_mask]
                    valid_labels = labels[valid_mask]
                    
                    if len(valid_outputs) > 0:
                        loss = combined_loss(valid_outputs, valid_labels)
                        val_loss += loss.item()
                        
                        # Collect predictions and labels
                        probs = torch.sigmoid(valid_outputs)
                        val_preds.append(probs.cpu().numpy())
                        val_labels.append(valid_labels.cpu().numpy())
        
        # Calculate metrics
        if len(train_preds) > 0 and len(val_preds) > 0:
            train_preds = np.concatenate([p.flatten() for p in train_preds])
            train_labels = np.concatenate([l.flatten() for l in train_labels])
            val_preds = np.concatenate([p.flatten() for p in val_preds])
            val_labels = np.concatenate([l.flatten() for l in val_labels])
            
            train_metrics = Metric(train_preds, train_labels)
            val_metrics = Metric(val_preds, val_labels)
            
            # Log metrics
            log_message = (f"Epoch {epoch+1}/{epochs} - "
                          f"Train Loss: {train_loss/len(train_dataloader):.6f}, "
                          f"Val Loss: {val_loss/len(val_dataloader):.6f}, "
                          f"Train AUC: {train_metrics[0]:.6f}, "
                          f"Val AUC: {val_metrics[0]:.6f}, "
                          f"Train AUPRC: {train_metrics[1]:.6f}, "
                          f"Val AUPRC: {val_metrics[1]:.6f}, "
                          f"Train MCC: {train_metrics[2]:.6f}, "
                          f"Val MCC: {val_metrics[2]:.6f}, "
                          f"Train Acc: {train_metrics[3]:.6f}, "
                          f"Val Acc: {val_metrics[3]:.6f}, "
                          f"Train Precision: {train_metrics[4]:.6f}, "
                          f"Val Precision: {val_metrics[4]:.6f}, "
                          f"Train Recall: {train_metrics[5]:.6f}, "
                          f"Val Recall: {val_metrics[5]:.6f}, "
                          f"Train F1: {train_metrics[6]:.6f}, "
                          f"Val F1: {val_metrics[6]:.6f}")
            
            Write_log(log, log_message)
            
            # Check for improvement
            current_val_auc = val_metrics[0]  # Using AUC as the primary metric
            if current_val_auc > best_val_auc:
                best_val_auc = current_val_auc
                best_epoch = epoch
                no_improvement = 0

                # Save the best model
                torch.save(model.state_dict(), output_weight + f'fold{fold}.ckpt')

                Write_log(log, f"Model saved at epoch {epoch+1} with validation AUC: {best_val_auc:.6f}")
            else:
                no_improvement += 1
                
            # Early stopping
            if no_improvement >= patience:
                Write_log(log, f"Early stopping at epoch {epoch+1}. Best epoch: {best_epoch+1} with validation AUC: {best_val_auc:.6f}")
                break

        # 🚀 更新学习率调度器
        scheduler.step()

    log.close()
    return best_val_auc


def main():
    parser = argparse.ArgumentParser(description='MVGNN-GTE Training')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    # 🏆 FINAL_BEST_CONFIG - 经过验证的最优配置
    parser.add_argument('--epochs', type=int, default=25, help='Number of epochs - 关键突破')
    parser.add_argument('--folds', type=int, default=5, help='Number of folds for cross-validation')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate - 关键突破')
    parser.add_argument('--hidden_dim', type=int, default=96, help='Hidden dimension')
    parser.add_argument('--num_encoder_layers', type=int, default=4, help='Number of encoder layers')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout rate')
    parser.add_argument('--patience', type=int, default=8, help='Early stopping patience')
    parser.add_argument('--focal_alpha', type=float, default=0.75, help='Focal loss alpha')
    parser.add_argument('--focal_gamma', type=float, default=3.0, help='Focal loss gamma')
    parser.add_argument('--class_weight_ratio', type=float, default=5.4, help='Class weight ratio')
    parser.add_argument('--output_root', default='./output/', help='Output directory')
    parser.add_argument('--num_workers', type=int, default=0, help='Number of workers for data loading')

    # 🏆 FINAL_BEST_CONFIG扩展参数 - 平衡性能和稳定性
    parser.add_argument('--use_coords_update', action='store_true', default=False, help='Enable coordinate updates (可选)')
    parser.add_argument('--pooling_ratio', type=float, default=0.3, help='Pooling ratio - 保守策略')
    parser.add_argument('--use_graph_collapse', action='store_true', default=True, help='Enable graph collapse mechanism')
    parser.add_argument('--num_iterations', type=int, default=1, help='Number of iterations - 平衡性能')

    args = parser.parse_args()

    # Set random seed
    Seed_everything(args.seed)

    # Load data
    df = pd.read_csv('./datasets/PRO_Train335.csv')
    print(f"Loaded dataset with {len(df)} samples")

    # Load protein data
    protein_data = {}
    feature_path = './feature/'

    print("Loading protein features...")
    for pdb_id in tqdm(df['ID'].unique()):
        try:
            protein_data[pdb_id] = (
                torch.load(feature_path + f'{pdb_id}_X.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_node_feature.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_mask.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_label.tensor', weights_only=True),
                torch.load(feature_path + f'{pdb_id}_adj.tensor', weights_only=True)
            )
        except FileNotFoundError:
            continue

    print(f"Successfully loaded {len(protein_data)} protein features")

    # Filter dataframe to only include proteins with features
    df = df[df['ID'].isin(protein_data.keys())].reset_index(drop=True)
    print(f"Filtered dataset size: {len(df)}")

    # 🏆 FINAL_BEST_CONFIG - 经过系统优化验证的最佳配置
    # 核心突破: learning_rate=0.001, epochs=25, use_unet_gt=True
    config = {
        'node_features': 1297,  # ESM2 (1280) + DSSP (14) + geometric (3)
        'edge_features': 16,
        'hidden_dim': args.hidden_dim,        # 96 - 最优
        'num_encoder_layers': args.num_encoder_layers,  # 4 - 最优
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': args.dropout,              # 0.3 - 最优
        'batch_size': args.batch_size,        # 4 - 最优
        'epochs': args.epochs,                # 25 - 关键突破
        'patience': args.patience,            # 8
        'learning_rate': args.learning_rate,  # 0.001 - 关键突破
        'focal_alpha': args.focal_alpha,      # 0.75 - 最优
        'focal_gamma': args.focal_gamma,      # 3.0 - 最优
        'class_weight_ratio': args.class_weight_ratio,  # 5.4 - 最优
        # 🚀 扩展功能 - 在FINAL_BEST_CONFIG基础上的增强
        'use_coords_update': getattr(args, 'use_coords_update', False),
        'pooling_ratio': getattr(args, 'pooling_ratio', 0.3),
        'use_graph_collapse': getattr(args, 'use_graph_collapse', True),
        'num_iterations': getattr(args, 'num_iterations', 1)
    }

    print("Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # Cross-validation
    kfold = KFold(n_splits=args.folds, shuffle=True, random_state=args.seed)
    fold_aucs = []

    for fold, (train_idx, val_idx) in enumerate(kfold.split(df)):
        print(f"\n{'='*60}")
        print(f"Training Fold {fold+1}/{args.folds}")
        print(f"{'='*60}")

        train_df_fold = df.iloc[train_idx].reset_index(drop=True)
        val_df_fold = df.iloc[val_idx].reset_index(drop=True)

        # Filter out samples without features
        train_df_fold = train_df_fold[train_df_fold['ID'].isin(protein_data.keys())].reset_index(drop=True)
        val_df_fold = val_df_fold[val_df_fold['ID'].isin(protein_data.keys())].reset_index(drop=True)

        print(f"Train samples: {len(train_df_fold)}")
        print(f"Validation samples: {len(val_df_fold)}")

        # Train model
        fold_auc = train_model(
            train_df_fold, val_df_fold, protein_data,
            MVGNN_GTE, config, fold+1, args.output_root, args
        )

        fold_aucs.append(fold_auc)
        print(f"Fold {fold+1} AUC: {fold_auc:.4f}")

    # Final results
    mean_auc = np.mean(fold_aucs)
    std_auc = np.std(fold_aucs)

    print(f"\n{'='*60}")
    print("Cross-Validation Results")
    print(f"{'='*60}")
    for i, auc in enumerate(fold_aucs):
        print(f"Fold {i+1}: AUC = {auc:.4f}")
    print(f"\nMean AUC: {mean_auc:.4f} ± {std_auc:.4f}")
    print(f"Best AUC: {max(fold_aucs):.4f}")

    # Save results
    results_file = os.path.join(args.output_root, 'cross_validation_results.txt')
    with open(results_file, 'w') as f:
        f.write("Cross-Validation Results\n")
        f.write("="*60 + "\n")
        for i, auc in enumerate(fold_aucs):
            f.write(f"Fold {i+1}: AUC = {auc:.4f}\n")
        f.write(f"\nMean AUC: {mean_auc:.4f} ± {std_auc:.4f}\n")
        f.write(f"Best AUC: {max(fold_aucs):.4f}\n")
        f.write(f"\nConfiguration:\n")
        for key, value in config.items():
            f.write(f"  {key}: {value}\n")


if __name__ == "__main__":
    main()
