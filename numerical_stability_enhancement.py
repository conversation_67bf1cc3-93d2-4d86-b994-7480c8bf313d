#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 数值稳定性增强脚本

这个脚本对MVGNN-GTE模型进行数值稳定性增强：
1. 改进权重初始化策略
2. 增强梯度裁剪机制
3. 优化归一化层配置
4. 添加数值稳定性检查
5. 实现自适应学习率调整
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.data import Data, Batch
import numpy as np
import math

from model_gte import MVGNN_GTE
from gte_block import GTE_Block

class NumericalStabilityEnhancer:
    """数值稳定性增强器"""
    
    def __init__(self):
        self.stability_config = {
            'gradient_clip_norm': 1.0,
            'weight_decay': 1e-5,
            'eps': 1e-8,
            'max_norm': 10.0,
            'init_scale': 0.02
        }
    
    def enhanced_weight_initialization(self, model):
        """增强的权重初始化"""
        print("🔧 应用增强的权重初始化...")
        
        def init_weights(m):
            if isinstance(m, nn.Linear):
                # 使用Xavier初始化，但添加小的随机扰动避免对称性
                nn.init.xavier_uniform_(m.weight, gain=1.0)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0.0)
                    # 添加小的随机扰动
                    with torch.no_grad():
                        m.bias.add_(torch.randn_like(m.bias) * 1e-6)
            
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.weight, 1.0)
                nn.init.constant_(m.bias, 0.0)
            
            elif isinstance(m, nn.Parameter):
                # 对于全局节点嵌入等参数
                if m.dim() > 1:
                    nn.init.xavier_uniform_(m, gain=0.1)  # 较小的初始化
                else:
                    nn.init.normal_(m, mean=0.0, std=0.01)
        
        model.apply(init_weights)
        
        # 特殊处理全局节点嵌入
        if hasattr(model, 'global_node_embedding'):
            with torch.no_grad():
                nn.init.normal_(model.global_node_embedding, mean=0.0, std=0.01)
        
        print("✅ 权重初始化完成")
    
    def add_gradient_clipping(self, model, optimizer):
        """添加梯度裁剪"""
        def clip_gradients():
            """梯度裁剪函数"""
            total_norm = torch.nn.utils.clip_grad_norm_(
                model.parameters(), 
                max_norm=self.stability_config['gradient_clip_norm']
            )
            return total_norm
        
        return clip_gradients
    
    def create_stable_optimizer(self, model, lr=1e-3):
        """创建数值稳定的优化器"""
        print("🔧 创建数值稳定的优化器...")
        
        # 分组参数，对不同类型的参数使用不同的权重衰减
        param_groups = []
        
        # 普通参数
        normal_params = []
        # 归一化层参数（不应用权重衰减）
        norm_params = []
        # 全局节点嵌入（使用较小的权重衰减）
        global_params = []
        
        for name, param in model.named_parameters():
            if 'norm' in name.lower() or 'bias' in name.lower():
                norm_params.append(param)
            elif 'global_node_embedding' in name:
                global_params.append(param)
            else:
                normal_params.append(param)
        
        if normal_params:
            param_groups.append({
                'params': normal_params,
                'weight_decay': self.stability_config['weight_decay']
            })
        
        if norm_params:
            param_groups.append({
                'params': norm_params,
                'weight_decay': 0.0
            })
        
        if global_params:
            param_groups.append({
                'params': global_params,
                'weight_decay': self.stability_config['weight_decay'] * 0.1
            })
        
        # 使用AdamW优化器，具有更好的数值稳定性
        optimizer = torch.optim.AdamW(
            param_groups,
            lr=lr,
            betas=(0.9, 0.999),
            eps=self.stability_config['eps'],
            amsgrad=True  # 使用AMSGrad变体提高稳定性
        )
        
        print(f"✅ 优化器创建完成，参数组数: {len(param_groups)}")
        return optimizer
    
    def create_lr_scheduler(self, optimizer, num_epochs=100):
        """创建学习率调度器"""
        print("🔧 创建学习率调度器...")
        
        # 使用余弦退火调度器，具有重启机制
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=10,  # 第一次重启的周期
            T_mult=2,  # 每次重启后周期的倍数
            eta_min=1e-6  # 最小学习率
        )
        
        print("✅ 学习率调度器创建完成")
        return scheduler
    
    def add_numerical_checks(self, model):
        """添加数值稳定性检查"""
        print("🔧 添加数值稳定性检查...")
        
        def check_hook(module, input, output):
            """检查模块输出的数值稳定性"""
            if isinstance(output, torch.Tensor):
                if torch.isnan(output).any():
                    print(f"⚠️ NaN detected in {module.__class__.__name__}")
                    return torch.nan_to_num(output, nan=0.0, posinf=1e6, neginf=-1e6)

                if torch.isinf(output).any():
                    print(f"⚠️ Inf detected in {module.__class__.__name__}")
                    return torch.clamp(output, min=-1e6, max=1e6)

                # 🔧 智能数值稳定性检查：根据层类型设置不同阈值
                max_val = output.abs().max()

                # 根据模块类型设置不同的阈值
                if isinstance(module, nn.Linear):
                    threshold = 300  # 🔧 进一步降低线性层阈值
                    clamp_range = (-100, 100)  # 🔧 更严格的裁剪范围
                elif isinstance(module, nn.LayerNorm):
                    threshold = 30   # 归一化层应该保持较小值
                    clamp_range = (-10, 10)
                else:
                    threshold = 100  # 其他层的默认阈值
                    clamp_range = (-50, 50)

                if max_val > threshold:
                    # 🔧 获取模块的详细名称用于调试
                    module_name = getattr(module, '_module_name', module.__class__.__name__)

                    # 只在真正异常时才报警和裁剪
                    if max_val > threshold * 1.5:  # 🔧 降低报警阈值
                        print(f"⚠️ Large values detected in {module_name}: {max_val:.1f}")

                    # 渐进式裁剪，避免突然的数值跳跃
                    output = torch.clamp(output, min=clamp_range[0], max=clamp_range[1])
                    return output
            
            elif hasattr(output, 'x'):  # 对于Data对象
                if torch.isnan(output.x).any():
                    print(f"⚠️ NaN detected in {module.__class__.__name__}.x")
                    output.x = torch.nan_to_num(output.x, nan=0.0, posinf=1e6, neginf=-1e6)
                
                if hasattr(output, 'pos') and torch.isnan(output.pos).any():
                    print(f"⚠️ NaN detected in {module.__class__.__name__}.pos")
                    output.pos = torch.nan_to_num(output.pos, nan=0.0, posinf=1e6, neginf=-1e6)
            
            return output
        
        # 为关键模块添加检查
        hooks = []
        for name, module in model.named_modules():
            if isinstance(module, (nn.Linear, nn.LayerNorm, GTE_Block)):
                # 🔧 为模块添加名称标识，便于调试
                module._module_name = name if name else module.__class__.__name__
                hook = module.register_forward_hook(check_hook)
                hooks.append(hook)
        
        print(f"✅ 为 {len(hooks)} 个模块添加了数值检查")
        return hooks
    
    def create_stable_loss_function(self):
        """创建数值稳定的损失函数"""
        print("🔧 创建数值稳定的损失函数...")
        
        class StableBCELoss(nn.Module):
            def __init__(self, pos_weight=None, label_smoothing=0.0):
                super().__init__()
                self.pos_weight = pos_weight
                self.label_smoothing = label_smoothing
            
            def forward(self, input, target):
                # 应用标签平滑
                if self.label_smoothing > 0:
                    target = target * (1 - self.label_smoothing) + 0.5 * self.label_smoothing
                
                # 使用数值稳定的BCE实现
                loss = F.binary_cross_entropy_with_logits(
                    input, target, 
                    pos_weight=self.pos_weight,
                    reduction='none'
                )
                
                # 过滤掉全局节点的损失（标签为-1）
                mask = target >= 0
                if mask.any():
                    loss = loss[mask].mean()
                else:
                    loss = torch.tensor(0.0, device=input.device, requires_grad=True)
                
                return loss
        
        loss_fn = StableBCELoss(label_smoothing=0.1)
        print("✅ 数值稳定的损失函数创建完成")
        return loss_fn
    
    def test_numerical_stability(self, model, test_data):
        """测试数值稳定性"""
        print("\n🧪 测试数值稳定性...")
        
        model.eval()
        stability_issues = []
        
        with torch.no_grad():
            # 测试正常输入
            try:
                output = model(batch=test_data)
                if torch.isnan(output.logits).any():
                    stability_issues.append("正常输入产生NaN")
                if torch.isinf(output.logits).any():
                    stability_issues.append("正常输入产生Inf")
            except Exception as e:
                stability_issues.append(f"正常输入失败: {e}")
            
            # 测试极端输入
            extreme_data = test_data.clone()
            extreme_data.x = extreme_data.x * 100  # 放大输入
            
            try:
                output = model(batch=extreme_data)
                if torch.isnan(output.logits).any():
                    stability_issues.append("极端输入产生NaN")
                if torch.isinf(output.logits).any():
                    stability_issues.append("极端输入产生Inf")
            except Exception as e:
                stability_issues.append(f"极端输入失败: {e}")
        
        if not stability_issues:
            print("✅ 数值稳定性测试通过")
        else:
            print("⚠️ 发现数值稳定性问题:")
            for issue in stability_issues:
                print(f"   - {issue}")
        
        return len(stability_issues) == 0
    
    def enhance_model(self, model, test_data=None):
        """对模型进行完整的数值稳定性增强"""
        print("🚀 开始数值稳定性增强")
        print("=" * 50)
        
        # 1. 权重初始化
        self.enhanced_weight_initialization(model)
        
        # 2. 创建优化器
        optimizer = self.create_stable_optimizer(model)
        
        # 3. 创建学习率调度器
        scheduler = self.create_lr_scheduler(optimizer)
        
        # 4. 添加数值检查
        hooks = self.add_numerical_checks(model)
        
        # 5. 创建稳定的损失函数
        loss_fn = self.create_stable_loss_function()
        
        # 6. 测试稳定性
        if test_data is not None:
            is_stable = self.test_numerical_stability(model, test_data)
        else:
            is_stable = True
        
        print("\n" + "=" * 50)
        if is_stable:
            print("🎉 数值稳定性增强完成！")
        else:
            print("⚠️ 数值稳定性增强完成，但仍存在问题")
        
        return {
            'model': model,
            'optimizer': optimizer,
            'scheduler': scheduler,
            'loss_fn': loss_fn,
            'hooks': hooks,
            'is_stable': is_stable
        }

def main():
    """主函数"""
    # 创建测试模型
    model = MVGNN_GTE(
        node_features=1041,
        edge_features=16,
        hidden_dim=128,
        num_encoder_layers=2,
        use_global_node=True
    )
    
    # 创建测试数据
    test_data = Data(
        x=torch.randn(51, 1041),  # 50个真实节点 + 1个全局节点
        pos=torch.randn(51, 3),
        edge_index=torch.randint(0, 50, (2, 200)),
        edge_attr=torch.randn(200, 16),
        y=torch.cat([torch.randint(0, 2, (50,)).float(), torch.tensor([-1.0])]),
        has_global_node=torch.tensor(True),
        num_real_nodes=torch.tensor(50)
    )
    
    batch = Batch.from_data_list([test_data])
    
    # 应用数值稳定性增强
    enhancer = NumericalStabilityEnhancer()
    results = enhancer.enhance_model(model, batch)
    
    return results

if __name__ == "__main__":
    results = main()
