# -*- coding: utf-8 -*-
"""
模型路径配置文件
"""

# ESM2模型配置
ESM2_MODEL_PATH = "/mnt/public_home/models/esm/esm2_t33_650M_UR50D.pt"

# T5模型配置
T5_MODEL_PATH = "/home/<USER>/MVGNN-PPIS-main/process_feature/pretrained_model/Rostlab/prot_t5_xl_uniref50"

# 特征维度配置
T5_DIM = 1024
ESM2_DIM = 1280
DSSP_DIM = 14

# 特征选择配置
USE_ESM2 = True  # True: 使用ESM2, False: 使用T5

# 计算总特征维度
LLM_DIM = ESM2_DIM if USE_ESM2 else T5_DIM
TOTAL_NODE_DIM = LLM_DIM + DSSP_DIM

print(f"🔧 特征配置: {'ESM2' if USE_ESM2 else 'T5'} ({LLM_DIM}维) + DSSP ({DSSP_DIM}维) = {TOTAL_NODE_DIM}维")
