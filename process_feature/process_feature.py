# -*- coding: utf-8 -*-
import pickle
import numpy as np
import torch
import os
from tqdm import tqdm
from pad_feature import parse_fasta_file
from get_dssp import get_dssp
from get_SC_position import PDBFeature
from get_SC_adj import prepare_adj
from get_T5embedding import getT5
from get_ESM2embedding import getESM2
from model_config import USE_ESM2, ESM2_MODEL_PATH, T5_MODEL_PATH
from pad_feature import prepare_features



def process(dataset, use_esm2=True):
    """
    处理蛋白质特征

    Args:
        dataset: 数据集路径
        use_esm2: 是否使用ESM2特征（True）还是T5特征（False）
    """
    if use_esm2:
        # 创建ESM2相关目录
        os.makedirs('../ESM2raw/', exist_ok=True)
        os.makedirs('../ESM2norm/', exist_ok=True)

        print("🔄 使用ESM2提取序列特征...")
        getESM2(dataset, '../ESM2raw/', '0', model_path=ESM2_MODEL_PATH)
        llm_raw_dir = '../ESM2raw/'
        llm_norm_dir = '../ESM2norm/'
    else:
        # 创建T5相关目录
        os.makedirs('../T5raw/', exist_ok=True)
        os.makedirs('../T5norm/', exist_ok=True)

        print("🔄 使用T5提取序列特征...")
        getT5(dataset, '../T5raw/', '0')
        llm_raw_dir = '../T5raw/'
        llm_norm_dir = '../T5norm/'

    data = parse_fasta_file(dataset)
    Max_protrans = []
    Min_protrans = []

    print(f"🔄 计算特征归一化参数...")
    for i, ID in tqdm(enumerate(data.keys()), desc="计算统计信息"):
        raw_protrans = np.load(llm_raw_dir + ID + ".npy")
        Max_protrans.append(np.max(raw_protrans, axis = 0))
        Min_protrans.append(np.min(raw_protrans, axis = 0))
        if i == len(data) - 1:
            Max_protrans = np.max(np.array(Max_protrans), axis = 0)
            Min_protrans = np.min(np.array(Min_protrans), axis = 0)
        elif i % 5000 == 0:
            Max_protrans = [np.max(np.array(Max_protrans), axis = 0)]
            Min_protrans = [np.min(np.array(Min_protrans), axis = 0)]

    print(f"🔄 归一化特征并保存...")
    for ID in tqdm(data.keys(), desc="归一化特征"):
        # 加载原始特征
        raw_protrans = np.load(llm_raw_dir + ID + ".npy")
        # 归一化
        protrans = (raw_protrans - Min_protrans) / (Max_protrans - Min_protrans + 1e-8)  # 添加小值避免除零
        # 保存归一化特征
        np.save(llm_norm_dir + ID + '.npy', protrans)

        #dssp
        get_dssp(ID, data[ID][0])

        # SC prosition
        PDBFeature(ID, '../datasets/pro_pro_pdb', '../SC_position')

        # SC_adj
        prepare_adj(ID,869)

        # last
        prepare_features(ID, data[ID][1], 869)

if __name__ == '__main__':
    # 设置使用ESM2还是T5
    USE_ESM2 = True  # 改为False使用T5

    print(f"🚀 开始特征处理，使用{'ESM2' if USE_ESM2 else 'T5'}特征")

    # 处理训练集
    fasta_file = '../datasets/PRO_Train_335.fa'
    print(f"📊 处理训练集: {fasta_file}")
    process(fasta_file, use_esm2=USE_ESM2)

    # 处理测试集
    fasta_file = '../datasets/PRO_Test_60.fa'
    print(f"📊 处理测试集: {fasta_file}")
    process(fasta_file, use_esm2=USE_ESM2)

    print("✅ 特征处理完成！")
