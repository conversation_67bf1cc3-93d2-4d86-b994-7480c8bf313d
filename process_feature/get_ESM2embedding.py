import torch
import esm
import re
import numpy as np
from tqdm import tqdm
import gc
import os

def getESM2(fasta_file, output_path_raw, gpu, model_path=None):
    """
    使用ESM2模型提取蛋白质序列特征

    Args:
        fasta_file: FASTA文件路径
        output_path_raw: 输出目录
        gpu: GPU设备号
        model_path: 本地模型路径（可选）
    """
    # 创建输出目录
    os.makedirs(output_path_raw, exist_ok=True)
    
    # 读取FASTA文件
    ID_list = []
    seq_list = []
    with open(fasta_file, "r") as f:
        lines = f.readlines()
    
    for line in lines:
        if line[0] == ">":
            ID_list.append(line[1:-1])
        elif line[0] != "0" and line[0] != "1":
            # ESM2不需要空格分隔的序列
            seq_list.append(line.strip())
    
    print(f"📊 加载了 {len(ID_list)} 个蛋白质序列")
    
    # 加载ESM2模型
    print("🔄 加载ESM2模型...")

    # 确定模型路径
    if model_path is None:
        model_path = "/mnt/public_home/models/esm/esm2_t33_650M_UR50D.pt"

    # 尝试加载本地模型
    if os.path.exists(model_path):
        print(f"✅ 使用本地模型: {model_path}")
        try:
            model, alphabet = esm.pretrained.load_model_and_alphabet_local(model_path)
        except Exception as e:
            print(f"⚠️ 本地模型加载失败: {e}")
            print("🔄 使用在线下载模型...")
            model, alphabet = esm.pretrained.esm2_t33_650M_UR50D()
    else:
        print(f"⚠️ 本地模型不存在: {model_path}")
        print("🔄 使用在线下载模型...")
        model, alphabet = esm.pretrained.esm2_t33_650M_UR50D()

    batch_converter = alphabet.get_batch_converter()
    
    # 设置设备
    device = torch.device(f'cuda:{gpu}' if torch.cuda.is_available() and gpu else 'cpu')
    model = model.to(device)
    model = model.eval()
    
    print(f"✅ ESM2模型加载完成，使用设备: {device}")
    
    batch_size = 1  # ESM2模型较大，使用小批次
    
    for i in tqdm(range(0, len(ID_list), batch_size), desc="提取ESM2特征"):
        if i + batch_size <= len(ID_list):
            batch_ID_list = ID_list[i:i + batch_size]
            batch_seq_list = seq_list[i:i + batch_size]
        else:
            batch_ID_list = ID_list[i:]
            batch_seq_list = seq_list[i:]
        
        # 清理序列中的非标准氨基酸
        batch_seq_list = [re.sub(r"[UZOB]", "X", sequence) for sequence in batch_seq_list]
        
        # 准备批次数据
        batch_labels = [(batch_ID_list[j], batch_seq_list[j]) for j in range(len(batch_ID_list))]
        batch_tokens = batch_converter(batch_labels)[2].to(device)
        
        # 提取特征
        with torch.no_grad():
            results = model(batch_tokens, repr_layers=[33], return_contacts=False)
        
        # 获取表示（最后一层）
        token_representations = results["representations"][33]
        
        # 处理每个序列
        for seq_idx, seq_id in enumerate(batch_ID_list):
            # 移除特殊token（<cls>和<eos>）
            seq_len = len(batch_seq_list[seq_idx])
            seq_repr = token_representations[seq_idx, 1:seq_len+1].cpu().numpy()
            
            # 保存特征
            np.save(os.path.join(output_path_raw, seq_id), seq_repr)
    
    print(f"✅ ESM2特征提取完成，保存到: {output_path_raw}")

if __name__ == '__main__':
    fasta_file = '../datasets/PRO_Train_335.fa'
    output_path_raw = './ESM2raw/'
    gpu = '0'
    # 使用本地模型路径
    local_model_path = "/mnt/public_home/models/esm/esm2_t33_650M_UR50D.pt"
    getESM2(fasta_file, output_path_raw, gpu, model_path=local_model_path)
