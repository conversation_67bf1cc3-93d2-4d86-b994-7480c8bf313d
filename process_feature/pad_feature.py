import pickle
import numpy as np
import pandas as pd
import torch
import joblib
from tqdm import tqdm

Dataset_Path = './datasets/'
Feature_Path = '../feature/'

# 导入配置
from model_config import USE_ESM2, TOTAL_NODE_DIM

NODE_DIM = TOTAL_NODE_DIM
# max_len = 882 # within train & tests


def get_pdb_xyz(pdb_file):
    current_pos = -1000
    X = []
    current_aa = {} # 'N', 'CA', 'C', 'O'
    for line in pdb_file:
        if (line[0:4].strip() == "ATOM" and int(line[22:26].strip()) != current_pos) or line[0:4].strip() == "TER":
            if current_aa != {}:
                X.append(current_aa["CA"]) # X.append([current_aa["N"], current_aa["CA"], current_aa["C"], current_aa["O"]])
                current_aa = {}
            if line[0:4].strip() != "TER":
                current_pos = int(line[22:26].strip())

        if line[0:4].strip() == "ATOM":
            atom = line[13:16].strip()
            if atom in ['N', 'CA', 'C', 'O']:
                xyz = np.array([line[30:38].strip(), line[38:46].strip(), line[46:54].strip()]).astype(np.float32)
                current_aa[atom] = xyz
    return np.array(X)


def prepare_features(pdb_id,label,max_len):
    # with open(Dataset_Path + "pdb/" + pdb_id + ".pdb", "r") as f:
    #     X = get_pdb_xyz(f.readlines()) # [L, 3]
    with open('../SC_position/'+pdb_id+'_psepos_SC.pkl', 'rb') as file:
        X = joblib.load(file)
    # 根据配置加载不同的语言模型特征
    if USE_ESM2:
        protrans = np.load(f'../ESM2norm/{pdb_id}.npy')
        print(f"🔄 使用ESM2特征: {pdb_id}, 维度: {protrans.shape}")
    else:
        protrans = np.load(f'../T5norm/{pdb_id}.npy')
        print(f"🔄 使用T5特征: {pdb_id}, 维度: {protrans.shape}")

    dssp = np.load(f'../dssp/{pdb_id}.npy') ## 107,14
    # one = np.load(Feature_Path + f'unbindfea/seponehot/{pdb_id}.npy')
    # res = np.load(Feature_Path + f'resAF/{pdb_id}.npy')

#     print(dssp, dssp.shape)
#     print('####')
#     print(protrans, protrans.shape)
    node_features = np.hstack([protrans,dssp])


    # Padding
    padded_X = np.zeros((max_len, 3))
    padded_X[:X.shape[0]] = X
    padded_X = torch.tensor(padded_X, dtype = torch.float)

    padded_node_features = np.zeros((max_len, NODE_DIM))
    padded_node_features[:node_features.shape[0]] = node_features
    padded_node_features = torch.tensor(padded_node_features, dtype = torch.float)

    masks = np.zeros(max_len)
    masks[:X.shape[0]] = 1
    masks = torch.tensor(masks, dtype = torch.long)
    zero_pad = torch.zeros(1000, dtype=torch.long)
    extended_masks = torch.cat((zero_pad, masks), dim=0)

    # 🔧 修复标签处理逻辑：标签长度应该与原始序列长度匹配
    # X.shape[0] 是填充后的长度(869)，我们需要的是原始序列长度
    original_seq_len = len(label)  # 标签长度就是原始序列长度

    # 创建填充后的标签张量
    padded_y = np.zeros(max_len)

    if len(label) > 0:
        # 将标签字符串转换为数字数组
        labels = np.array([int(digit) for digit in label])
        # 将原始标签放入填充张量的前面部分
        padded_y[:original_seq_len] = labels

        # 统计正负样本
        pos_count = np.sum(labels == 1)
        neg_count = np.sum(labels == 0)

        print(f"✅ {pdb_id}: 标签处理成功")
        print(f"   原始序列长度: {original_seq_len}")
        print(f"   正样本: {pos_count}, 负样本: {neg_count}")
        print(f"   正样本比例: {pos_count/original_seq_len:.4f}")
    else:
        print(f"❌ {pdb_id}: 标签为空")

    padded_y = torch.tensor(padded_y, dtype=torch.float)

    # Save
    torch.save(padded_X, Feature_Path + f'/{pdb_id}_X.tensor')
    torch.save(padded_node_features, Feature_Path + f'/{pdb_id}_node_feature.tensor')
    torch.save(masks, Feature_Path + f'/{pdb_id}_mask.tensor')
    torch.save(padded_y, Feature_Path + f'/{pdb_id}_label.tensor')


def parse_fasta_file(file_path):
    protein_dict = {}
    with open(file_path, 'r') as file:
        lines = file.readlines()
    current_protein = None
    sequence = ""
    labels = ""
    for line in lines:
        line = line.strip()
        if line.startswith('>'):
            if current_protein:
                protein_dict[current_protein] = [sequence, labels]
                sequence = ""
                labels = ""
            current_protein = line[1:]  # 去掉 '>' 符号
        elif current_protein:
            if not sequence:
                sequence = line
            else:
                labels = line
    if current_protein:
        protein_dict[current_protein] = [sequence, labels]

    return protein_dict




if __name__ == '__main__':


    proteindata = parse_fasta_file('./datasets/Test_315.fa')# 使用函数

    for ID in proteindata.keys():
        # if ID !='4cej1_B':
        prepare_features(ID,proteindata[ID][1],869)
