#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🚀 MVGNN-PPIS 性能突破测试脚本
快速验证改进效果
"""

import subprocess
import sys
import os
from pathlib import Path

def run_breakthrough_test():
    """运行性能突破测试"""
    print("🚀 MVGNN-PPIS 性能突破测试")
    print("=" * 60)
    print("基于学习曲线诊断的系统性改进验证")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        'train_optimal.py',
        'datasets/PRO_Train335.csv',
        'datasets/protein_data.npz'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 运行改进的训练脚本
    print("\n🚀 开始运行改进的训练脚本...")
    print("使用性能突破配置:")
    print("  - hidden_dim: 128 (原96)")
    print("  - num_encoder_layers: 5 (原4)")
    print("  - dropout: 0.4 (原0.3)")
    print("  - batch_size: 8 (原4)")
    print("  - learning_rate: 2e-4 (原1e-3)")
    print("  - epochs: 40 (原25)")
    print("  - 强制图格式: True")
    print("  - 梯度累积: 2步")
    print("  - 梯度裁剪: 0.5")
    
    # 构建命令
    cmd = [
        sys.executable, 'train_optimal.py',
        '--hidden_dim', '128',
        '--num_encoder_layers', '5', 
        '--dropout', '0.4',
        '--batch_size', '8',
        '--epochs', '40',
        '--patience', '12',
        '--learning_rate', '2e-4',
        '--folds', '5',
        '--output_root', './output_breakthrough/',
        '--seed', '42'
    ]
    
    print(f"\n执行命令: {' '.join(cmd)}")
    print("\n" + "="*60)
    
    try:
        # 运行训练
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        if result.returncode == 0:
            print("✅ 训练完成!")
            print("\n📊 训练输出:")
            print(result.stdout)
            
            # 检查结果文件
            results_file = Path('./output_breakthrough/cross_validation_results.txt')
            if results_file.exists():
                print("\n📋 结果文件内容:")
                with open(results_file, 'r') as f:
                    print(f.read())
            
            return True
        else:
            print("❌ 训练失败!")
            print(f"错误代码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 训练超时 (1小时)")
        return False
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        return False

def quick_test():
    """快速测试 - 只运行1个fold"""
    print("🔥 快速测试模式 (1 fold)")
    print("=" * 40)
    
    cmd = [
        sys.executable, 'train_optimal.py',
        '--hidden_dim', '128',
        '--num_encoder_layers', '5',
        '--dropout', '0.4', 
        '--batch_size', '8',
        '--epochs', '5',  # 只训练5个epoch
        '--patience', '3',
        '--learning_rate', '2e-4',
        '--folds', '1',   # 只运行1个fold
        '--output_root', './output_quick_test/',
        '--seed', '42'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)  # 10分钟超时
        
        if result.returncode == 0:
            print("✅ 快速测试完成!")
            print("\n📊 输出:")
            print(result.stdout[-1000:])  # 显示最后1000字符
            return True
        else:
            print("❌ 快速测试失败!")
            print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 快速测试超时")
        return False
    except Exception as e:
        print(f"❌ 快速测试出错: {e}")
        return False

def main():
    """主函数"""
    print("选择测试模式:")
    print("1. 快速测试 (1 fold, 5 epochs, ~10分钟)")
    print("2. 完整测试 (5 folds, 40 epochs, ~1小时)")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == '1':
            print("\n🔥 开始快速测试...")
            success = quick_test()
            break
        elif choice == '2':
            print("\n🚀 开始完整测试...")
            success = run_breakthrough_test()
            break
        elif choice == '3':
            print("👋 退出测试")
            return
        else:
            print("❌ 无效选择，请输入 1、2 或 3")
    
    if success:
        print("\n🎉 测试成功完成!")
        print("\n📋 下一步建议:")
        print("1. 查看学习曲线分析结果")
        print("2. 对比改进前后的性能")
        print("3. 根据结果进一步调优")
    else:
        print("\n😞 测试失败")
        print("\n🔧 故障排除建议:")
        print("1. 检查数据文件是否存在")
        print("2. 检查GPU内存是否足够")
        print("3. 检查依赖包是否安装完整")

if __name__ == "__main__":
    main()
