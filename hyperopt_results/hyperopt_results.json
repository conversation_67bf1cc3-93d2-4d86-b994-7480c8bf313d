[{"config_id": 1, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 64, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0001}, "timestamp": "2025-07-31T10:41:41.808935"}, {"config_id": 2, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 96, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0001}, "timestamp": "2025-07-31T10:41:41.954581"}, {"config_id": 3, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 128, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0001}, "timestamp": "2025-07-31T10:41:42.138807"}, {"config_id": 4, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 160, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0001}, "timestamp": "2025-07-31T10:41:42.385469"}, {"config_id": 5, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 64, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0002}, "timestamp": "2025-07-31T10:41:42.503798"}, {"config_id": 6, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 96, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0002}, "timestamp": "2025-07-31T10:41:42.691478"}, {"config_id": 7, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 128, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0002}, "timestamp": "2025-07-31T10:41:42.897648"}, {"config_id": 8, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 160, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0002}, "timestamp": "2025-07-31T10:41:43.111140"}, {"config_id": 9, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 64, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0005}, "timestamp": "2025-07-31T10:41:43.207419"}, {"config_id": 10, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 96, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0005}, "timestamp": "2025-07-31T10:41:43.329376"}, {"config_id": 11, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 128, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0005}, "timestamp": "2025-07-31T10:41:43.500163"}, {"config_id": 12, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 160, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.0005}, "timestamp": "2025-07-31T10:41:43.745230"}, {"config_id": 13, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 64, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.001}, "timestamp": "2025-07-31T10:41:43.842325"}, {"config_id": 14, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 96, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.001}, "timestamp": "2025-07-31T10:41:43.964223"}, {"config_id": 15, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 128, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.001}, "timestamp": "2025-07-31T10:41:44.144698"}, {"config_id": 16, "auc": 0.0, "config": {"node_features": 1297, "use_golden_features": false, "edge_features": 16, "hidden_dim": 160, "num_encoder_layers": 4, "k_neighbors": 30, "batch_size": 4, "epochs": 20, "patience": 8, "dropout": 0.3, "use_unet_gt": true, "pooling_ratio": 0.3, "use_coords_update": false, "use_gcn_fusion": true, "use_stable_scheduler": true, "num_iterations": 1, "use_global_node": true, "focal_alpha": 0.75, "focal_gamma": 3.0, "class_weight_ratio": 5.4, "learning_rate": 0.001}, "timestamp": "2025-07-31T10:41:44.490077"}]