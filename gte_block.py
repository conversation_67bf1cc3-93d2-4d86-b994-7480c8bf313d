import torch
import torch.nn as nn
import numpy as np
import math

# 🔧 安全导入torch_geometric
try:
    from torch_geometric.utils import scatter
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    print("⚠️ torch_geometric not available, using torch_scatter fallback")
    try:
        from torch_scatter import scatter
        TORCH_GEOMETRIC_AVAILABLE = True
    except ImportError:
        print("❌ Neither torch_geometric nor torch_scatter available")
        TORCH_GEOMETRIC_AVAILABLE = False
        # 提供fallback函数
        def scatter(src, index, dim=0, reduce='mean', dim_size=None):
            """简单的scatter fallback实现"""
            if reduce == 'mean':
                return torch.zeros(dim_size, src.size(-1), device=src.device, dtype=src.dtype)
            else:
                return torch.zeros(dim_size, src.size(-1), device=src.device, dtype=src.dtype)

# 🚀 【管理员】导入torch_scatter - 解决批处理难题的关键工具
try:
    from torch_scatter import scatter_mean
    TORCH_SCATTER_AVAILABLE = True
except ImportError:
    print("⚠️ torch_scatter 不可用，使用fallback scatter_mean")
    TORCH_SCATTER_AVAILABLE = False

    # 创建fallback scatter_mean函数
    def scatter_mean(src, index, dim=0, dim_size=None):
        """Fallback scatter_mean implementation"""
        if dim_size is None:
            dim_size = index.max().item() + 1

        # 创建输出张量
        out_shape = list(src.shape)
        out_shape[dim] = dim_size
        out = torch.zeros(out_shape, dtype=src.dtype, device=src.device)
        count = torch.zeros(dim_size, dtype=torch.long, device=src.device)

        # 手动聚合
        for i in range(src.size(dim)):
            idx = index[i].item()
            if dim == 0:
                out[idx] += src[i]
            else:
                out[:, idx] += src[:, i]
            count[idx] += 1

        # 计算平均值
        count = count.clamp(min=1).float()
        if dim == 0:
            out = out / count.unsqueeze(-1)
        else:
            out = out / count.unsqueeze(0).unsqueeze(-1)

        return out


def unsorted_segment_sum(data, segment_ids, num_segments):
    """🚀 安全的分段求和操作 - 修复维度不匹配问题"""
    # 🔧 【关键修复】安全的维度检查和处理
    if data.size(0) == 0:
        # 空数据情况
        result_shape = (num_segments, data.size(1))
        return data.new_zeros(result_shape)

    if segment_ids.size(0) != data.size(0):
        # 维度不匹配，截断到最小尺寸
        min_size = min(data.size(0), segment_ids.size(0))
        print(f"⚠️ unsorted_segment_sum维度不匹配，截断: data={data.size(0)}, segment_ids={segment_ids.size(0)} -> {min_size}")
        data = data[:min_size]
        segment_ids = segment_ids[:min_size]

    # 确保segment_ids在有效范围内
    segment_ids = torch.clamp(segment_ids, 0, num_segments - 1)

    try:
        result_shape = (num_segments, data.size(1))
        result = data.new_full(result_shape, 0)
        segment_ids_expanded = segment_ids.unsqueeze(-1).expand(-1, data.size(1))
        result.scatter_add_(0, segment_ids_expanded, data)
        return result
    except Exception as e:
        print(f"❌ unsorted_segment_sum失败: {e}")
        print(f"   data.shape: {data.shape}")
        print(f"   segment_ids.shape: {segment_ids.shape}")
        # 降级处理：返回零张量
        return data.new_zeros((num_segments, data.size(1)))


def unsorted_segment_mean(data, segment_ids, num_segments):
    """Segment mean operation for scatter operations."""
    result_shape = (num_segments, data.size(1))
    segment_ids = segment_ids.unsqueeze(-1).expand(-1, data.size(1))
    result = data.new_full(result_shape, 0)
    count = data.new_full(result_shape, 0)
    result.scatter_add_(0, segment_ids, data)
    count.scatter_add_(0, segment_ids, torch.ones_like(data))
    return result / count.clamp(min=1)


class EGC_Layer(nn.Module):
    """Equivariant Graph Convolutional Layer - 结构分支"""
    
    def __init__(self, input_nf, output_nf, hidden_nf, edges_in_d=0, 
                 act_fn=nn.SiLU(), residual=True, attention=False, 
                 normalize=False, coords_agg='mean', tanh=False):
        super(EGC_Layer, self).__init__()
        input_edge = input_nf * 2
        self.residual = residual
        self.attention = attention
        self.normalize = normalize
        self.coords_agg = coords_agg
        self.tanh = tanh
        self.epsilon = 1e-8
        edge_coords_nf = 1

        # 🔧 边消息网络 - 添加数值稳定性控制
        class StableLinear(nn.Module):
            def __init__(self, in_features, out_features):
                super().__init__()
                self.linear = nn.Linear(in_features, out_features)
                # 🔧 暴露必要的属性
                self.in_features = in_features
                self.out_features = out_features

            def forward(self, x):
                # 🔧 输入裁剪
                x = torch.clamp(x, min=-10.0, max=10.0)
                out = self.linear(x)
                # 🔧 输出裁剪
                out = torch.clamp(out, min=-50.0, max=50.0)
                return out

        self.edge_mlp = nn.Sequential(
            StableLinear(input_edge + edge_coords_nf + edges_in_d, hidden_nf),
            act_fn,
            StableLinear(hidden_nf, hidden_nf),
            act_fn)

        # 🔧 节点更新网络 - 添加数值稳定性控制
        self.node_mlp = nn.Sequential(
            StableLinear(hidden_nf + input_nf, hidden_nf),
            act_fn,
            StableLinear(hidden_nf, output_nf))

        # 坐标更新网络
        layer = nn.Linear(hidden_nf, 1, bias=False)
        torch.nn.init.xavier_uniform_(layer.weight, gain=0.001)

        coord_mlp = []
        coord_mlp.append(nn.Linear(hidden_nf, hidden_nf))
        coord_mlp.append(act_fn)
        coord_mlp.append(layer)
        if self.tanh:
            coord_mlp.append(nn.Tanh())
        self.coord_mlp = nn.Sequential(*coord_mlp)

        # 注意力网络
        if self.attention:
            self.att_mlp = nn.Sequential(
                nn.Linear(hidden_nf, 1),
                nn.Sigmoid())

    def edge_model(self, source, target, radial, edge_attr):
        """计算边特征 - 数值稳定版本"""
        if edge_attr is None:
            out = torch.cat([source, target, radial], dim=1)
        else:
            # 🚀 【终极修复】安全的边特征处理
            try:
                # 检查所有输入张量的第一维是否匹配
                if not (source.size(0) == target.size(0) == radial.size(0) == edge_attr.size(0)):
                    min_size = min(source.size(0), target.size(0), radial.size(0), edge_attr.size(0))
                    # print(f"⚠️ 边特征维度不匹配，截断到最小尺寸: {min_size}")  # 🔧 注释掉调试输出
                    source = source[:min_size]
                    target = target[:min_size]
                    radial = radial[:min_size]
                    edge_attr = edge_attr[:min_size]

                # 检查edge_attr的特征维度并调整
                expected_dim = self.edge_mlp[0].in_features - source.size(1) - target.size(1) - radial.size(1)
                if edge_attr.size(1) != expected_dim:
                    # 如果维度不匹配，使用线性层调整
                    proj_key = f'edge_attr_proj_{edge_attr.size(1)}_{expected_dim}'
                    if not hasattr(self, proj_key):
                        proj_layer = nn.Linear(edge_attr.size(1), expected_dim).to(edge_attr.device)
                        setattr(self, proj_key, proj_layer)
                        # 注册为模块参数
                        self.add_module(proj_key, proj_layer)
                    edge_attr = getattr(self, proj_key)(edge_attr)

                out = torch.cat([source, target, radial, edge_attr], dim=1)
            except Exception as e:
                print(f"⚠️ 边特征处理失败: {e}")
                # 降级处理：只使用基础特征
                out = torch.cat([source, target, radial], dim=1)

        out = self.edge_mlp(out)
        if self.attention:
            att_val = self.att_mlp(out)
            out = out * att_val

        # 🔧 关键修复：使用tanh限制消息范围到[-1, 1]
        out = torch.tanh(out)

        return out

    def node_model(self, x, edge_index, edge_attr, node_attr):
        """更新节点特征"""
        row, _ = edge_index  # col不需要使用
        agg = unsorted_segment_sum(edge_attr, row, num_segments=x.size(0))
        if node_attr is not None:
            agg = torch.cat([x, agg, node_attr], dim=1)
        else:
            agg = torch.cat([x, agg], dim=1)
        out = self.node_mlp(agg)
        if self.residual:
            out = x + out
        return out, agg

    def coord_model(self, coord, edge_index, coord_diff, edge_feat):
        """更新坐标 - 数值稳定版本"""
        row, _ = edge_index  # col不需要使用

        # 🔧 阶段二优化：进一步减弱坐标更新强度
        coord_update = self.coord_mlp(edge_feat)
        coord_update = torch.clamp(coord_update, min=-1.0, max=1.0)  # 阶段二：更严格限制 -2→-1

        trans = coord_diff * coord_update

        # 强制使用mean聚合以提高稳定性
        if self.coords_agg == 'sum':
            agg = unsorted_segment_sum(trans, row, num_segments=coord.size(0))
        elif self.coords_agg == 'mean':
            agg = unsorted_segment_mean(trans, row, num_segments=coord.size(0))
        else:
            raise Exception('Wrong coords_agg parameter' % self.coords_agg)

        # 🔧 阶段二优化：更严格的坐标更新限制，更小步长
        agg = torch.clamp(agg, min=-2.0, max=2.0)  # 阶段二：更严格限制 -4→-2
        coord = coord + agg * 0.005  # 阶段二：更小步长 0.01→0.005

        return coord

    def coord2radial(self, edge_index, coord):
        """计算径向距离和坐标差"""
        row, col = edge_index
        coord_diff = coord[row] - coord[col]
        radial = torch.sum(coord_diff**2, 1).unsqueeze(1)

        if self.normalize:
            norm = torch.sqrt(radial).detach() + self.epsilon
            coord_diff = coord_diff / norm

        return radial, coord_diff

    def forward(self, h, edge_index, coord, edge_attr=None, node_attr=None):
        """前向传播"""
        row, col = edge_index
        radial, coord_diff = self.coord2radial(edge_index, coord)

        edge_feat = self.edge_model(h[row], h[col], radial, edge_attr)
        coord = self.coord_model(coord, edge_index, coord_diff, edge_feat)
        h, _ = self.node_model(h, edge_index, edge_feat, node_attr)  # agg不需要使用

        return h, coord, edge_attr


class GT_Layer(nn.Module):
    """Graph Transformer Layer - 特征分支"""
    
    def __init__(self, hidden_dim, edge_dim, num_heads=4, dropout=0.1):
        super(GT_Layer, self).__init__()
        self.hidden_dim = hidden_dim
        self.edge_dim = edge_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        # 多头注意力的线性变换
        self.W_Q = nn.Linear(hidden_dim, hidden_dim, bias=False)
        self.W_K = nn.Linear(hidden_dim, hidden_dim, bias=False)
        self.W_V = nn.Linear(hidden_dim, hidden_dim, bias=False)
        self.edge_projection = nn.Linear(edge_dim, hidden_dim, bias=False)
        
        # 输出投影
        self.O_node = nn.Linear(hidden_dim, hidden_dim)
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim)
        )
        
        # 归一化层
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout)
        # 🔧 添加更多dropout层以减少过拟合
        self.dropout_heavy = nn.Dropout(min(dropout * 1.5, 0.5))  # 更强的dropout

    def propagate_attention(self, edge_index, node_feats_q, node_feats_k, 
                          node_feats_v, edge_feats_projection):
        """传播注意力 - 核心的边调制注意力机制"""
        row, col = edge_index
        
        # 🚀 改进2：增强注意力计算
        # 计算点积注意力分数
        alpha = (node_feats_k[row] * node_feats_q[col]).sum(-1, keepdim=True)  # [num_edges, num_heads, 1]

        # 缩放
        alpha = alpha / np.sqrt(self.head_dim)

        # 🚀 新增：边特征增强的注意力
        # 使用边特征作为额外的注意力偏置
        edge_bias = (edge_feats_projection * node_feats_q[col]).sum(-1, keepdim=True)  # [num_edges, num_heads, 1]
        alpha = alpha + 0.1 * edge_bias  # 边特征贡献

        # 裁剪防止数值不稳定
        alpha = alpha.clamp(-5.0, 5.0)
        
        # 应用softmax
        alpha_sum = alpha.sum(-1, keepdim=True)  # [num_edges, num_heads, 1]
        alphax = torch.exp(alpha_sum.clamp(-5.0, 5.0))  # [num_edges, num_heads, 1]
        
        # 加权聚合
        wV = node_feats_v[row] * alphax  # [num_edges, num_heads, head_dim]
        
        # 按目标节点聚合
        output = unsorted_segment_sum(wV.view(-1, self.hidden_dim), col, 
                                    num_segments=node_feats_q.size(0))
        z = unsorted_segment_sum(alphax.view(-1, self.num_heads), col, 
                               num_segments=node_feats_q.size(0))
        
        # 归一化
        output = output / (z.sum(-1, keepdim=True) + 1e-6)
        
        return output

    def forward(self, h, edge_index, edge_attr):
        """前向传播"""
        # 缓存输入用于残差连接
        h_in = h
        
        # 第一层归一化
        h = self.norm1(h)
        
        # 计算Q, K, V
        node_feats_q = self.W_Q(h).view(-1, self.num_heads, self.head_dim)
        node_feats_k = self.W_K(h).view(-1, self.num_heads, self.head_dim)
        node_feats_v = self.W_V(h).view(-1, self.num_heads, self.head_dim)
        
        # 🔧 修复：边特征投影 - 处理维度不匹配
        # 🚀 【修复】安全的边特征维度适配
        try:
            if edge_attr.size(1) != self.edge_projection.in_features:
                # 使用唯一的适配器名称
                adapter_key = f'edge_attr_adapter_{edge_attr.size(1)}_{self.edge_projection.in_features}'
                if not hasattr(self, adapter_key):
                    adapter = nn.Linear(
                        edge_attr.size(1),
                        self.edge_projection.in_features
                    ).to(edge_attr.device)
                    setattr(self, adapter_key, adapter)
                    # 注册为模块参数
                    self.add_module(adapter_key, adapter)
                edge_attr_adapted = getattr(self, adapter_key)(edge_attr)
            else:
                edge_attr_adapted = edge_attr
        except Exception as e:
            print(f"⚠️ 边特征适配失败: {e}")
            # 降级处理：使用零填充或截断
            target_dim = self.edge_projection.in_features
            if edge_attr.size(1) < target_dim:
                # 零填充
                padding = torch.zeros(edge_attr.size(0), target_dim - edge_attr.size(1), device=edge_attr.device)
                edge_attr_adapted = torch.cat([edge_attr, padding], dim=1)
            else:
                # 截断
                edge_attr_adapted = edge_attr[:, :target_dim]

        edge_feats_projection = self.edge_projection(edge_attr_adapted).view(-1, self.num_heads, self.head_dim)
        
        # 多头注意力
        h_att = self.propagate_attention(edge_index, node_feats_q, node_feats_k, 
                                       node_feats_v, edge_feats_projection)
        
        # 输出投影和dropout
        h_att = self.dropout(self.O_node(h_att))
        
        # 第一个残差连接
        h = h_in + h_att
        
        # 第二层归一化
        h_in2 = h
        h = self.norm2(h)
        
        # 前馈网络
        h_ffn = self.ffn(h)
        h_ffn = self.dropout(h_ffn)
        
        # 第二个残差连接
        h = h_in2 + h_ffn
        
        return h


class GT_Layer_UNet(nn.Module):
    """
    U-Net式的Graph Transformer层 - 在特征分支内部实现层次化建模

    架构：GT_Block -> Pool -> GT_Block_middle -> Unpool -> Skip Connection -> GT_Block_final
    """

    def __init__(self, hidden_dim, edge_dim, num_heads=4, dropout=0.1,
                 pooling_ratio=0.5, use_asap_pooling=True):
        super(GT_Layer_UNet, self).__init__()

        self.hidden_dim = hidden_dim
        self.edge_dim = edge_dim
        self.pooling_ratio = pooling_ratio
        self.use_asap_pooling = use_asap_pooling

        # === U-Net的各个组件 ===

        # 1. 初始GT层 (编码器)
        self.gt_encoder = GT_Layer(
            hidden_dim=hidden_dim,
            edge_dim=edge_dim,
            num_heads=num_heads,
            dropout=dropout
        )

        # 2. 图池化层
        if use_asap_pooling:
            # 使用ASCE-PPIS的ASAP池化
            self.pooling = ASAP_Pooling_Simplified(
                in_channels=hidden_dim,
                ratio=pooling_ratio,
                dropout_att=dropout
            )
        else:
            # 使用简单的TopK池化
            from torch_geometric.nn import TopKPooling
            self.pooling = TopKPooling(hidden_dim, ratio=pooling_ratio)

        # 3. 中间GT层 (在池化后的小图上工作)
        self.gt_middle = GT_Layer(
            hidden_dim=hidden_dim,
            edge_dim=edge_dim,
            num_heads=num_heads,
            dropout=dropout
        )

        # 4. 特征融合层 (跳跃连接后的处理)
        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # 拼接后的维度
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 🔧 阶段二优化：稳定U-Net特征融合
        self.norm_after_fusion = nn.LayerNorm(hidden_dim)

        # 5. 最终GT层 (解码器)
        self.gt_decoder = GT_Layer(
            hidden_dim=hidden_dim,
            edge_dim=edge_dim,
            num_heads=num_heads,
            dropout=dropout
        )

        # 🔧 阶段一优化：控制U-Net模块的残差贡献
        self.residual_weight = nn.Parameter(torch.tensor(0.01))

        # 🚀 【新】门控融合机制 (替换复杂的Graph Collapse)
        # 用于决定局部信息和全局信息的融合比例
        self.fusion_gate_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # 输入是局部+全局特征的拼接
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.Sigmoid()  # 输出门控权重 [0,1]
        )

        # 🚀 异构边类型处理
        self.edge_type_embedding = nn.Embedding(4, edge_dim)  # 4种边类型
        self.edge_type_weights = nn.Parameter(torch.ones(4) / 4)  # 边类型权重

    def forward(self, h, edge_index, edge_attr, batch=None):
        """
        U-Net式前向传播

        Args:
            h: 节点特征 [num_nodes, hidden_dim]
            edge_index: 边索引 [2, num_edges]
            edge_attr: 边特征 [num_edges, edge_dim]
            batch: 批次信息 (可选)

        Returns:
            h_out: 输出节点特征 [num_nodes, hidden_dim]
        """
        # 保存原始输入用于最终残差连接
        h_residual = h

        # === 编码阶段 ===
        # 1. 初始GT层处理
        h_encoded = self.gt_encoder(h, edge_index, edge_attr)

        # === 池化阶段 ===
        # 2. 图池化
        if self.use_asap_pooling:
            # 使用ASAP池化
            pooled_result = self.pooling(h_encoded, edge_index, edge_attr, batch)
            h_pooled = pooled_result['node_features']
            edge_index_pooled = pooled_result['edge_index']
            edge_attr_pooled = pooled_result['edge_attr']
            perm = pooled_result['perm']  # 被保留节点的索引
        else:
            # 使用TopK池化
            h_pooled, edge_index_pooled, edge_attr_pooled, _, perm, _ = \
                self.pooling(h_encoded, edge_index, edge_attr, batch)

        # 🔍 调试信息：验证池化效果
        if hasattr(self, '_debug_counter'):
            self._debug_counter += 1
        else:
            self._debug_counter = 1

        # 注释掉调试信息保持日志简洁
        # if self._debug_counter <= 3:  # 只打印前3次
        #     print(f"🔍 U-Net Debug - Original nodes: {h_encoded.size(0)}, Pooled nodes: {h_pooled.size(0)}, Ratio: {h_pooled.size(0)/h_encoded.size(0):.3f}")

        # === 中间处理阶段 ===
        # 3. 在池化后的小图上运行GT层 (虚拟全局节点)
        h_virtual = self.gt_middle(h_pooled, edge_index_pooled, edge_attr_pooled)

        # === 【新】门控融合机制 (替换Graph Collapse) ===
        # 4. 图反池化 (将池化后的全局信息放回原始图大小)
        h_unpooled = self.unpool(h_virtual, perm, h_encoded.size(0))

        # 5. 【新】门控融合 - 让模型自己学习局部vs全局信息的权重
        # 计算融合门控权重，决定全局信息和局部信息的比例
        fusion_input = torch.cat([h_encoded, h_unpooled], dim=-1)  # [num_nodes, hidden_dim*2]
        fusion_gate = self.fusion_gate_layer(fusion_input)  # [num_nodes, hidden_dim]

        # 6. 根据门控权重进行融合
        # fusion_gate接近0时更相信局部信息，接近1时更相信全局信息
        h_fused = (1 - fusion_gate) * h_encoded + fusion_gate * h_unpooled
        # 🔧 融合后归一化稳定特征分布
        h_fused = self.norm_after_fusion(h_fused)

        # === 最终处理阶段 ===
        # 6. 最终GT层
        h_decoded = self.gt_decoder(h_fused, edge_index, edge_attr)

        # 7. 残差连接
        h_out = h_residual + self.residual_weight * h_decoded

        return h_out

    # 🔧 【已弃用】Graph Collapse方法 - 已被门控融合替代
    # def atom_to_virtual_interaction(self, h_atom, h_virtual, perm=None, edge_index=None):
    #     """原子到虚拟节点的交互 (A2V) - 已被门控融合替代"""
    #     pass

    # def virtual_to_atom_interaction(self, h_virtual, h_atom, perm=None, edge_index=None):
    #     """虚拟节点到原子的交互 (V2A) - 已被门控融合替代"""
    #     pass

    def unpool(self, h_pooled, perm, original_size):
        """
        图反池化：将池化后的特征放回原始图大小

        Args:
            h_pooled: 池化后的节点特征 [num_pooled_nodes, hidden_dim]
            perm: 被保留节点的索引 [num_pooled_nodes]
            original_size: 原始图的节点数

        Returns:
            h_unpooled: 反池化后的特征 [original_size, hidden_dim]
        """
        device = h_pooled.device
        h_unpooled = torch.zeros(original_size, self.hidden_dim, device=device)
        h_unpooled[perm] = h_pooled.to(h_unpooled.dtype).to(h_unpooled.dtype)
        return h_unpooled


class ASAP_Pooling_Simplified(nn.Module):
    """
    简化版的ASAP池化，适配到GT_Layer_UNet中
    基于ASCE-PPIS的ASAP_Pooling，但简化了接口
    """

    def __init__(self, in_channels, ratio=0.5, dropout_att=0.1, negative_slope=0.2):
        super(ASAP_Pooling_Simplified, self).__init__()

        self.in_channels = in_channels
        self.ratio = ratio
        self.negative_slope = negative_slope
        self.dropout_att = dropout_att

        # 注意力机制组件
        self.lin_q = nn.Linear(in_channels, in_channels)
        self.gat_att = nn.Linear(2 * in_channels, 1)

        # 节点重要性评分网络
        self.score_net = nn.Sequential(
            nn.Linear(in_channels, in_channels // 2),
            nn.ReLU(),
            nn.Dropout(dropout_att),
            nn.Linear(in_channels // 2, 1),
            nn.Sigmoid()
        )

        self.reset_parameters()

    def reset_parameters(self):
        self.lin_q.reset_parameters()
        self.gat_att.reset_parameters()
        for layer in self.score_net:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()

    def forward(self, x, edge_index, edge_attr=None, batch=None):
        """
        简化的ASAP池化前向传播

        Returns:
            dict: 包含池化结果的字典
                - node_features: 池化后的节点特征
                - edge_index: 池化后的边索引
                - edge_attr: 池化后的边特征
                - perm: 被保留节点的索引
        """
        if batch is None:
            batch = edge_index.new_zeros(x.size(0))

        num_nodes = x.size(0)

        # 计算节点重要性分数
        fitness = self.score_net(x).view(-1)

        # 选择top-k个最重要的节点
        num_pooled_nodes = max(1, int(self.ratio * num_nodes))
        _, perm = torch.topk(fitness, num_pooled_nodes, sorted=True)

        # 提取池化后的节点特征
        x_pooled = x[perm]

        # 构建池化后的边
        edge_index_pooled, edge_attr_pooled = self.extract_subgraph(
            edge_index, edge_attr, perm, num_nodes
        )

        return {
            'node_features': x_pooled,
            'edge_index': edge_index_pooled,
            'edge_attr': edge_attr_pooled,
            'perm': perm
        }

    def extract_subgraph(self, edge_index, edge_attr, perm, num_nodes):
        """提取子图的边 - 修复维度不匹配问题"""
        # 🔧 【关键修复】确保edge_index和edge_attr维度一致
        if edge_attr is not None and edge_index.size(1) != edge_attr.size(0):
            print(f"⚠️ extract_subgraph维度不匹配: edge_index={edge_index.size(1)}, edge_attr={edge_attr.size(0)}")
            # 截断到最小尺寸
            min_edges = min(edge_index.size(1), edge_attr.size(0))
            edge_index = edge_index[:, :min_edges]
            edge_attr = edge_attr[:min_edges]
            print(f"   截断后: edge_index={edge_index.size(1)}, edge_attr={edge_attr.size(0)}")

        # 创建节点映射
        node_mask = torch.zeros(num_nodes, dtype=torch.bool, device=edge_index.device)
        node_mask[perm] = True

        # 找到连接池化节点的边
        edge_mask = node_mask[edge_index[0]] & node_mask[edge_index[1]]
        edge_index_pooled = edge_index[:, edge_mask]

        # 重新映射节点索引
        node_idx_map = torch.full((num_nodes,), -1, dtype=torch.long, device=edge_index.device)
        node_idx_map[perm] = torch.arange(len(perm), device=edge_index.device)

        edge_index_pooled = node_idx_map[edge_index_pooled]

        # 🚀 【安全修复】处理边特征
        if edge_attr is not None:
            try:
                edge_attr_pooled = edge_attr[edge_mask]
            except Exception as e:
                print(f"⚠️ 边特征池化失败: {e}")
                print(f"   edge_attr.shape: {edge_attr.shape}, edge_mask.shape: {edge_mask.shape}")
                # 降级处理：创建零特征
                edge_attr_pooled = torch.zeros((edge_mask.sum().item(), edge_attr.size(1)),
                                             dtype=edge_attr.dtype, device=edge_attr.device)
        else:
            edge_attr_pooled = None

        return edge_index_pooled, edge_attr_pooled


class GCNLayer(nn.Module):
    """
    图卷积层 - 用于全局信息传播
    基于成功MVGNN模型的GCN设计
    """
    def __init__(self, input_dim, output_dim, dropout=0.1):
        super(GCNLayer, self).__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.weight = nn.Parameter(torch.FloatTensor(input_dim, output_dim))
        self.dropout = nn.Dropout(dropout)
        self.reset_parameters()

    def reset_parameters(self):
        stdv = 1. / math.sqrt(self.output_dim)
        self.weight.data.uniform_(-stdv, stdv)

    def forward(self, x, adj, mask=None):
        """
        Args:
            x: 节点特征 [B, N, D] 或 [total_nodes, D]
            adj: 邻接矩阵 [B, N, N] 或 [total_nodes, total_nodes]
            mask: 节点掩码 [B, N] 或 [total_nodes] (可选)
        """
        if x.dim() == 3:  # 批处理格式 [B, N, D]
            B, N, D = x.size()
            # 扩展权重用于批处理
            expanded_weight = self.weight.unsqueeze(0).expand(B, -1, -1)

            # 应用掩码
            if mask is not None:
                x = x * mask.unsqueeze(-1)
                adj = adj * mask.unsqueeze(-1) * mask.unsqueeze(1)

            # 图卷积
            y = torch.bmm(adj, x)
            y = torch.bmm(y, expanded_weight)

            # 应用掩码到输出
            if mask is not None:
                y = y * mask.unsqueeze(-1)
        else:  # 图格式 [total_nodes, D]
            # 简单的矩阵乘法
            y = torch.mm(adj, x)
            y = torch.mm(y, self.weight)

            if mask is not None:
                y = y * mask.unsqueeze(-1)

        return self.dropout(y)


class GTE_Block(nn.Module):
    """
    Graph Transformer-Equivariant Block - 融合成功MVGNN设计的改进版本

    核心改进：
    1. 融合局部注意力 + 全局GCN的混合设计
    2. 添加直达初始层的残差连接
    3. 简化架构，提高稳定性
    4. 可选的坐标更新（默认关闭以提高稳定性）
    """

    def __init__(self, hidden_dim, edge_dim, num_heads=4, dropout=0.1,
                 egnn_layers=1, attention=True, residual=True, use_unet_gt=False,
                 pooling_ratio=0.5, num_iterations=1, use_global_node=True,
                 use_coords_update=False, use_gcn_fusion=True):
        super(GTE_Block, self).__init__()

        self.use_unet_gt = use_unet_gt
        self.pooling_ratio = pooling_ratio
        self.num_iterations = num_iterations  # 🚀 默认设为1以提高稳定性
        self.hidden_dim = hidden_dim
        self.edge_dim = edge_dim
        self.use_global_node = use_global_node
        self.use_coords_update = use_coords_update  # 🚀 新增：是否更新坐标
        self.use_gcn_fusion = use_gcn_fusion  # 🚀 新增：是否使用GCN融合

        # 🚀 【关键改进1】：添加GCN层用于全局信息传播
        if self.use_gcn_fusion:
            self.gcn_layer = GCNLayer(hidden_dim, hidden_dim, dropout=dropout)
            # 门控融合权重
            self.fusion_gate = nn.Linear(hidden_dim * 2, hidden_dim)
            self.theta_weight = nn.Parameter(torch.tensor(0.5))  # 可学习的融合权重

        # 🚀 结构更新层：等变图卷积层（可选）
        if self.use_coords_update:
            self.egc_layer = EGC_Layer(
                input_nf=hidden_dim,
                output_nf=hidden_dim,
                hidden_nf=hidden_dim,
                edges_in_d=edge_dim,
                act_fn=nn.SiLU(),
                residual=residual,
                attention=attention,
                normalize=False,
                coords_agg='mean',
                tanh=False
            )

        # 特征更新层：图Transformer层 (可选U-Net版本)
        if self.use_unet_gt:
            self.gt_layer = GT_Layer_UNet(
                hidden_dim=hidden_dim,
                edge_dim=edge_dim,
                num_heads=num_heads,
                dropout=dropout,
                pooling_ratio=self.pooling_ratio
            )
        else:
            self.gt_layer = GT_Layer(
                hidden_dim=hidden_dim,
                edge_dim=edge_dim,
                num_heads=num_heads,
                dropout=dropout
            )

        # 🚀 增强版动态边特征重计算模块
        # 输入: [距离(1) + 对数距离(1) + 方向向量(3) + 坐标差(3)] = 8维
        self.edge_recompute_mlp = nn.Sequential(
            nn.Linear(8, edge_dim),  # 几何特征 -> 边特征维度
            nn.LayerNorm(edge_dim),
            nn.SiLU(),
            nn.Dropout(dropout * 0.1),
            nn.Linear(edge_dim, edge_dim),  # 深度变换
            nn.LayerNorm(edge_dim),
            nn.SiLU(),
            nn.Linear(edge_dim, edge_dim),  # 最终输出
            nn.Tanh()  # 限制输出范围
        )

        # 🚀 【关键改进2】：直达初始层的残差连接
        self.initial_residual_gate = nn.Linear(hidden_dim * 2, hidden_dim)
        self.initial_residual_weight = nn.Parameter(torch.tensor(0.7))  # 类似成功模型的0.7权重

        # 🚀 迭代过程中的归一化层
        self.norm_h = nn.LayerNorm(hidden_dim)
        if self.use_coords_update:
            self.norm_coords = nn.LayerNorm(3)  # 坐标归一化

        # 🔧 添加更强的dropout以减少过拟合
        self.dropout_heavy = nn.Dropout(min(dropout * 1.5, 0.5))

        # 🚀 迭代式残差更新权重（可学习）
        self.residual_weight_h = nn.Parameter(torch.tensor(0.1))  # 特征残差权重
        if self.use_coords_update:
            self.residual_weight_coords = nn.Parameter(torch.tensor(0.1))  # 坐标残差权重

        # 🚀 增强版最终融合层（迭代完成后）
        self.final_fusion = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim * 2),  # 先扩展
            nn.LayerNorm(hidden_dim * 2),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),  # 再压缩
            nn.LayerNorm(hidden_dim),
            nn.SiLU(),
            nn.Linear(hidden_dim, hidden_dim)  # 最终输出
        )

        # 🚀 全局上下文节点机制模块
        if self.use_global_node:
            # 全局节点更新器：汇总所有真实节点信息并更新全局状态
            self.global_updater = nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim),  # 输入: [旧全局特征, 聚合后的局部特征]
                nn.LayerNorm(hidden_dim),
                nn.SiLU(),
                nn.Dropout(dropout * 0.5),
                nn.Linear(hidden_dim, hidden_dim)
            )

            # 广播融合器：将全局信息融合到每个真实节点
            self.broadcast_fusion = nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim),  # 输入: [局部更新特征, 全局广播特征]
                nn.LayerNorm(hidden_dim),
                nn.SiLU(),
                nn.Linear(hidden_dim, hidden_dim)
            )

            # 门控机制：控制全局信息的融合程度
            self.broadcast_gate = nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.Sigmoid()  # 输出0-1的门控权重
            )

        # 🚀 多尺度注意力融合
        self.multi_scale_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

    def recompute_edge_features(self, coords, edge_index, iteration=0):
        """
        🚀 增强版动态边特征重计算 - 包含更丰富的几何信息

        Args:
            coords: 更新后的坐标 [num_nodes, 3]
            edge_index: 边索引 [2, num_edges]
            iteration: 当前迭代次数，用于自适应特征计算

        Returns:
            edge_attr_updated: 更新后的边特征 [num_edges, edge_dim]
        """
        row, col = edge_index

        # 1. 基础几何特征
        coord_diff = coords[row] - coords[col]  # [num_edges, 3]
        distances = torch.norm(coord_diff, dim=1, keepdim=True)  # [num_edges, 1]

        # 2. 🚀 增强几何特征
        # 方向向量（单位化）
        directions = coord_diff / (distances + 1e-8)  # [num_edges, 3]

        # 距离的对数变换（处理长程和短程交互）
        log_distances = torch.log(distances + 1e-8)  # [num_edges, 1]

        # 3. 🚀 迭代自适应权重
        iteration_weight = torch.sigmoid(torch.tensor(iteration / self.num_iterations).to(coords.device))

        # 4. 组合所有几何特征
        geometric_features = torch.cat([
            distances,           # [num_edges, 1] - 原始距离
            log_distances,       # [num_edges, 1] - 对数距离
            directions,          # [num_edges, 3] - 方向向量
            coord_diff,          # [num_edges, 3] - 坐标差
        ], dim=1)  # [num_edges, 8]

        # 5. 通过增强MLP映射到边特征空间
        edge_attr_updated = self.edge_recompute_mlp(geometric_features)  # [num_edges, edge_dim]

        # 6. 🚀 【关键修复】安全的迭代自适应融合
        if hasattr(self, '_previous_edge_attr') and self._previous_edge_attr is not None:
            try:
                # 检查维度是否完全匹配
                if (self._previous_edge_attr.size(0) == edge_attr_updated.size(0) and
                    self._previous_edge_attr.size(1) == edge_attr_updated.size(1)):
                    # 维度匹配时进行融合
                    edge_attr_updated = (1 - iteration_weight) * self._previous_edge_attr + iteration_weight * edge_attr_updated
                else:
                    # 维度不匹配时，只使用当前的边特征（跳过融合）
                    print(f"⚠️ Edge dimension mismatch: {self._previous_edge_attr.shape} vs {edge_attr_updated.shape}, skipping fusion")
            except Exception as e:
                print(f"⚠️ Edge fusion failed: {e}, using current edge features only")
                # 发生任何错误都跳过融合，只使用当前边特征

        # 保存当前边特征用于下次迭代
        self._previous_edge_attr = edge_attr_updated.detach().clone()

        return edge_attr_updated

    def convert_knn_to_edge_index(self, E_idx, batch_size, seq_len, k_neighbors):
        """将k近邻格式转换为edge_index格式"""
        edge_indices = []
        # edge_attrs = []  # 暂时不需要

        for b in range(batch_size):
            for i in range(seq_len):
                for k in range(k_neighbors):
                    j = E_idx[b, i, k].item()
                    if j < seq_len:  # 确保索引有效
                        edge_indices.append([i + b * seq_len, j + b * seq_len])

        if len(edge_indices) == 0:
            # 如果没有边，创建自环
            edge_indices = [[i, i] for i in range(batch_size * seq_len)]

        edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
        return edge_index

    def prepare_edge_features(self, h_E, E_idx, batch_size, seq_len, k_neighbors):
        """准备边特征，从[B, L, K, D]格式转换为[num_edges, D]格式"""
        edge_features = []

        for b in range(batch_size):
            for i in range(seq_len):
                for k in range(k_neighbors):
                    j = E_idx[b, i, k].item()
                    if j < seq_len:  # 确保索引有效
                        edge_features.append(h_E[b, i, k])

        if len(edge_features) == 0:
            # 如果没有边特征，创建零特征
            edge_features = [torch.zeros(h_E.size(-1), device=h_E.device)
                           for _ in range(batch_size * seq_len)]

        return torch.stack(edge_features)

    def _build_adjacency_matrix(self, edge_index, num_nodes):
        """
        从edge_index构建邻接矩阵
        Args:
            edge_index: [2, num_edges]
            num_nodes: 节点数量
        Returns:
            adj: [num_nodes, num_nodes] 邻接矩阵
        """
        adj = torch.zeros(num_nodes, num_nodes, device=edge_index.device, dtype=torch.float)
        if edge_index.size(1) > 0:
            adj[edge_index[0], edge_index[1]] = 1.0
            # 确保对称性（无向图）
            adj = adj + adj.t()
            adj = torch.clamp(adj, 0, 1)
            # 添加自环
            adj.fill_diagonal_(1.0)
        return adj

    def forward(self, batch):
        """
        🚀 迭代式协同进化前向传播

        核心改进：在单个Block内进行T次结构-特征的微调迭代
        每次迭代中：
        1. 结构更新：使用当前特征和坐标更新坐标
        2. 边特征重计算：根据新坐标动态重计算边特征
        3. 特征更新：使用当前特征和新边特征更新特征
        4. 残差连接：平滑更新过程

        Args:
            batch: torch_geometric.data.Batch 对象

        Returns:
            batch: 更新了节点特征和坐标的 batch 对象
        """
        # 🚀 关键修复：在处理每个新batch前，必须重置历史状态
        self._previous_edge_attr = None

        # 初始化
        h = batch.x.clone()  # [num_nodes, hidden_dim]
        coords = batch.pos.clone()  # [num_nodes, 3]
        edge_index = batch.edge_index  # [2, num_edges]
        initial_edge_attr = batch.edge_attr  # [num_edges, edge_dim]

        # 🚀 【重构】全局上下文节点机制：使用real_nodes_mask进行批处理
        has_global_node = False
        real_nodes_mask = None

        # 🎯 【智能广播系统】使用real_nodes_mask识别真实节点和全局节点
        if self.use_global_node and hasattr(batch, 'real_nodes_mask'):
            try:
                real_nodes_mask = batch.real_nodes_mask
                # 检查是否有全局节点（real_nodes_mask中的False值）
                has_global_node = (~real_nodes_mask).any().item()

                if has_global_node:
                    # print(f"✅ 检测到全局节点，真实节点数: {real_nodes_mask.sum().item()}, 总节点数: {real_nodes_mask.size(0)}")  # 🔧 注释掉调试输出
                    pass

            except Exception as e:
                print(f"⚠️ real_nodes_mask检查失败: {e}")
                has_global_node = False
                # 创建fallback mask（假设所有节点都是真实节点）
                real_nodes_mask = torch.ones(h.size(0), dtype=torch.bool, device=h.device)
        else:
            has_global_node = False
            # 创建fallback mask（假设所有节点都是真实节点）
            real_nodes_mask = torch.ones(h.size(0), dtype=torch.bool, device=h.device)

        # 🚀 【终极修复】安全地分离真实节点和全局节点
        if has_global_node:
            # 🔧 【关键修复】确保edge_index和edge_attr维度一致
            if edge_index.size(1) != initial_edge_attr.size(0):
                # print(f"⚠️ 边索引和边特征维度不匹配: edge_index={edge_index.size(1)}, edge_attr={initial_edge_attr.size(0)}")  # 🔧 注释掉调试输出
                # 截断到最小尺寸
                min_edges = min(edge_index.size(1), initial_edge_attr.size(0))
                edge_index = edge_index[:, :min_edges]
                initial_edge_attr = initial_edge_attr[:min_edges]
                # print(f"   截断后: edge_index={edge_index.size(1)}, edge_attr={initial_edge_attr.size(0)}")  # 🔧 注释掉调试输出

            # 安全地检查边索引范围
            max_node_idx = real_nodes_mask.size(0) - 1
            valid_edge_mask = (edge_index[0] <= max_node_idx) & (edge_index[1] <= max_node_idx)

            if valid_edge_mask.any():
                # 只处理有效的边
                valid_edge_index = edge_index[:, valid_edge_mask]
                valid_edge_attr = initial_edge_attr[valid_edge_mask] if initial_edge_attr.size(0) > 0 else initial_edge_attr

                # 分离边：只保留真实节点间的边
                real_edge_mask = real_nodes_mask[valid_edge_index[0]] & real_nodes_mask[valid_edge_index[1]]
                real_edge_index = valid_edge_index[:, real_edge_mask]
                real_edge_attr = valid_edge_attr[real_edge_mask] if valid_edge_attr.size(0) > 0 else valid_edge_attr
            else:
                # 没有有效边，创建空的边索引和特征
                real_edge_index = torch.empty((2, 0), dtype=torch.long, device=edge_index.device)
                real_edge_attr = torch.empty((0, initial_edge_attr.size(1) if initial_edge_attr.size(0) > 0 else 16), device=edge_index.device)

            # 🔧 【关键修复】重新映射边索引到真实节点的局部索引
            if real_edge_index.size(1) > 0:
                # 创建从全局索引到局部索引的映射
                real_node_indices = torch.where(real_nodes_mask)[0]  # 真实节点的全局索引
                global_to_local = torch.full((h.size(0),), -1, dtype=torch.long, device=h.device)
                global_to_local[real_node_indices] = torch.arange(len(real_node_indices), device=h.device)

                # 重新映射边索引
                real_edge_index = global_to_local[real_edge_index]
        else:
            # 没有全局节点，所有节点都是真实节点
            real_edge_index = edge_index
            real_edge_attr = initial_edge_attr

        # 保存初始状态用于最终融合
        h_initial = h.clone()

        # 🚀 增强版迭代式协同进化核心循环 - 实现真正的双向信息流
        # 第一次迭代前，使用batch中最初的边特征
        edge_attr_for_iter = initial_edge_attr

        # 注释掉调试信息保持日志简洁
        # print(f"🚨 DEBUG: Starting {self.num_iterations} iterations")

        for t in range(self.num_iterations):
            # print(f"🚨 DEBUG: Iteration {t+1}/{self.num_iterations} started")

            if has_global_node:
                # 🚀 全局上下文节点机制的三步处理流程

                # === 步骤1：局部信息交互（只在真实节点间进行） ===
                h_real = h[real_nodes_mask]  # 提取真实节点特征
                coords_real = coords[real_nodes_mask]  # 提取真实节点坐标

                # 在真实节点子图上进行结构更新（如果启用坐标更新）
                if self.use_coords_update:
                    _, coords_real_updated, _ = self.egc_layer(
                        h_real, real_edge_index, coords_real, edge_attr=real_edge_attr
                    )
                else:
                    coords_real_updated = coords_real  # 保持坐标不变

                # 重新计算真实节点间的边特征（如果坐标有更新）
                if self.use_coords_update:
                    edge_attr_real_updated = self.recompute_edge_features(
                        coords_real_updated, real_edge_index, iteration=t
                    )
                else:
                    edge_attr_real_updated = real_edge_attr  # 保持原始边特征

                # 在真实节点子图上进行特征更新
                h_real_gt = self.gt_layer(h_real, real_edge_index, edge_attr_real_updated)

                # 🚀 【关键改进1】：添加GCN全局信息传播
                if self.use_gcn_fusion:
                    # 构建真实节点的邻接矩阵（简化版本，基于edge_index）
                    adj_real = self._build_adjacency_matrix(real_edge_index, h_real.size(0))
                    h_real_gcn = self.gcn_layer(h_real, adj_real)

                    # 门控融合GT和GCN的输出
                    fusion_input = torch.cat([h_real_gt, h_real_gcn], dim=1)
                    fusion_gate = torch.sigmoid(self.fusion_gate(fusion_input))
                    h_real_gt = fusion_gate * h_real_gt + (1 - fusion_gate) * h_real_gcn

                # 🚀 【关键改进2】：直达初始层的残差连接
                h_real_initial = h_initial[real_nodes_mask]
                residual_input = torch.cat([h_real_gt, h_real_initial], dim=1)
                h_real_residual = self.initial_residual_gate(residual_input)
                h_real_gt = (1 - self.initial_residual_weight) * h_real_gt + self.initial_residual_weight * h_real_residual

                # === 步骤2：【智能广播系统】信息汇总至全局节点 ===
                # 🚀 使用scatter_mean自动处理批处理和单样本情况
                if hasattr(batch, 'batch') and batch.batch is not None:
                    # 获取真实节点的batch索引
                    real_batch_indices = batch.batch[real_nodes_mask]

                    # 🚀 【汇总】使用scatter_mean按样本分组聚合真实节点特征
                    batch_size = batch.ptr.size(0) - 1 if hasattr(batch, 'ptr') else real_batch_indices.max().item() + 1
                    h_aggregated = scatter_mean(h_real_gt, real_batch_indices, dim=0, dim_size=batch_size)
                    # h_aggregated shape: [batch_size, hidden_dim]

                    # 获取当前全局节点特征
                    h_global_old = h[~real_nodes_mask]  # [num_global_nodes, hidden_dim]
                else:
                    # 单个样本情况
                    h_aggregated = torch.mean(h_real_gt, dim=0, keepdim=True)  # [1, hidden_dim]
                    h_global_old = h[~real_nodes_mask]  # [1, hidden_dim]

                # 🚀 【修复】确保全局节点和聚合特征维度匹配
                # 在批处理情况下，h_global_old可能有多个全局节点，需要与h_aggregated对齐
                if h_global_old.size(0) != h_aggregated.size(0):
                    if hasattr(batch, 'batch') and batch.batch is not None:
                        # 批处理情况：将聚合特征扩展到匹配全局节点数量
                        global_batch_indices = batch.batch[~real_nodes_mask]

                        # 🚀 【修复】确保索引不越界
                        max_agg_idx = h_aggregated.size(0) - 1
                        global_batch_indices = torch.clamp(global_batch_indices, 0, max_agg_idx)

                        h_aggregated_expanded = h_aggregated[global_batch_indices]
                    else:
                        # 单样本情况：扩展聚合特征
                        h_aggregated_expanded = h_aggregated.expand(h_global_old.size(0), -1)
                else:
                    h_aggregated_expanded = h_aggregated

                # 更新全局节点特征
                global_input = torch.cat([h_global_old, h_aggregated_expanded], dim=1)  # [num_global, 2*hidden_dim]
                h_global_new = self.global_updater(global_input)  # [num_global, hidden_dim]

                # 🔧 裁剪全局节点特征
                h_global_new = torch.clamp(h_global_new, min=-5.0, max=5.0)

                # === 步骤3：【智能广播系统】全局信息广播并融合 ===
                # 🚀 【广播】将全局信息精确派发给属于该样本的所有真实节点
                if hasattr(batch, 'batch') and batch.batch is not None:
                    # 获取真实节点的batch索引
                    real_batch_indices = batch.batch[real_nodes_mask]

                    # 🚀 【修复】确保索引不越界
                    max_batch_idx = h_global_new.size(0) - 1
                    real_batch_indices = torch.clamp(real_batch_indices, 0, max_batch_idx)

                    # 将全局信息广播到对应的真实节点
                    h_global_broadcast = h_global_new[real_batch_indices]  # [num_real, hidden_dim]
                else:
                    # 单个样本情况
                    h_global_broadcast = h_global_new.expand_as(h_real_gt)

                # 融合局部和全局信息
                fusion_input = torch.cat([h_real_gt, h_global_broadcast], dim=1)  # [num_real, 2*hidden_dim]
                gate = self.broadcast_gate(fusion_input)  # [num_real, hidden_dim]
                h_fused = self.broadcast_fusion(fusion_input)  # [num_real, hidden_dim]

                # 🔧 裁剪门控和融合输出
                gate = torch.clamp(gate, min=0.0, max=1.0)
                h_fused = torch.clamp(h_fused, min=-5.0, max=5.0)

                # 门控融合
                h_real_final = (1 - gate) * h_real_gt + gate * h_fused

                # 重新组合所有节点的特征
                h_new = h.clone()
                h_new[real_nodes_mask] = h_real_final
                if (~real_nodes_mask).any():
                    h_new[~real_nodes_mask] = h_global_new
                h = h_new

                # 更新坐标
                coords_new = coords.clone()
                coords_new[real_nodes_mask] = coords_real_updated
                coords = coords_new

                # 为下一次迭代准备边特征
                edge_attr_for_iter = edge_attr_real_updated

            else:
                # 🚀 原始处理流程（无全局节点）
                # === 第1步：结构更新（如果启用坐标更新）===
                if self.use_coords_update:
                    _, coords_updated, _ = self.egc_layer(
                        h, edge_index, coords, edge_attr=edge_attr_for_iter
                    )
                else:
                    coords_updated = coords  # 保持坐标不变

                # === 第2步：安全的边特征更新（如果坐标有更新）===
                if self.use_coords_update:
                    # 🚀 【关键修复】确保边特征维度一致性
                    try:
                        edge_attr_updated = self.recompute_edge_features(coords_updated, edge_index, iteration=t)
                        # 验证维度是否匹配
                        if edge_attr_updated.size(0) != edge_index.size(1):
                            print(f"⚠️ 边特征维度不匹配: {edge_attr_updated.size(0)} vs {edge_index.size(1)}, 使用原始边特征")
                            edge_attr_updated = edge_attr_for_iter
                    except Exception as e:
                        print(f"⚠️ 边特征重计算失败: {e}, 使用原始边特征")
                        edge_attr_updated = edge_attr_for_iter
                else:
                    edge_attr_updated = edge_attr_for_iter  # 保持原始边特征

                # === 第3步：特征更新 ===
                h_gt = self.gt_layer(h, edge_index, edge_attr_updated)

                # 🔧 立即裁剪GT层输出，防止数值爆炸
                h_gt = torch.clamp(h_gt, min=-5.0, max=5.0)

                # === 第4步：自适应残差更新 ===
                iteration_progress = (t + 1) / self.num_iterations
                adaptive_h_weight = self.residual_weight_h * (1 + 0.5 * iteration_progress)

                # 平滑地更新特征
                h_update = h + adaptive_h_weight * (h_gt - h)

                # 🔧 在归一化前先裁剪，防止极值影响归一化
                h_update = torch.clamp(h_update, min=-5.0, max=5.0)

                h = self.norm_h(h_update)

                # 只有在启用坐标更新时才更新坐标
                if self.use_coords_update:
                    adaptive_coords_weight = self.residual_weight_coords * (1 + 0.3 * iteration_progress)
                    coords_update = coords + adaptive_coords_weight * (coords_updated - coords)
                    coords_update = torch.clamp(coords_update, min=-20.0, max=20.0)
                    coords = self.norm_coords(coords_update)

                # 🔧 在训练时应用更强的dropout以减少过拟合
                if self.training:
                    h = self.dropout_heavy(h)

                # 为下一次迭代准备边特征
                edge_attr_for_iter = edge_attr_updated.detach()

            # 🔧 更严格的数值稳定性保护
            h = torch.clamp(h, min=-3.0, max=3.0)  # 🔧 更严格的特征裁剪
            coords = torch.clamp(coords, min=-20.0, max=20.0)  # 🔧 更严格的坐标裁剪

            # 🚀 数值稳定性检查
            if torch.isnan(h).any() or torch.isinf(h).any():
                print(f"🚨 WARNING: Invalid values in h at iteration {t+1}")
            if torch.isnan(coords).any() or torch.isinf(coords).any():
                print(f"🚨 WARNING: Invalid values in coords at iteration {t+1}")

            # 诊断输出（已禁用以保持日志简洁）
            # h_norm = h.norm().item()
            # coords_norm = coords.norm().item()
            # print(f"🔍 Iter {t+1}: h_norm={h_norm:.2f}, coords_norm={coords_norm:.2f}")

        # 🚀 增强版最终融合：简化版本以节省内存
        # 🔧 暂时禁用多尺度注意力以节省GPU内存
        # h_attended, _ = self.multi_scale_attention(
        #     h.unsqueeze(0), h.unsqueeze(0), h.unsqueeze(0)
        # )
        # h_attended = h_attended.squeeze(0)

        # 直接使用当前特征作为attended特征
        h_attended = h

        # 2. 特征融合
        h_combined = torch.cat([h_initial, h_attended], dim=-1)
        h_final = self.final_fusion(h_combined)

        # 3. 残差连接
        h_final = h_final + h_initial * 0.1  # 保留一些初始信息

        # 最终数值稳定性保护
        h_final = torch.clamp(h_final, min=-3.0, max=3.0)  # 更严格的范围

        # 🚀 【修复】更新batch对象，包括边特征的传递
        batch.x = h_final
        batch.pos = coords
        # 将最后一次迭代计算出的边特征存回batch对象
        batch.edge_attr = edge_attr_for_iter

        return batch



    def _forward_standard_format(self, h_V, coords, h_E, E_idx, mask_V):
        """
        🚀 标准格式的迭代式协同进化前向传播（保持向后兼容）
        """
        batch_size, seq_len, hidden_dim = h_V.shape
        k_neighbors = E_idx.size(-1)

        # 将批量数据展平为图格式
        h_V_flat = h_V.view(-1, hidden_dim)  # [B*L, D]
        coords_flat = coords.view(-1, 3)     # [B*L, 3]

        # 转换边格式
        edge_index = self.convert_knn_to_edge_index(E_idx, batch_size, seq_len, k_neighbors)
        initial_edge_attr = self.prepare_edge_features(h_E, E_idx, batch_size, seq_len, k_neighbors)

        # 移动到正确的设备
        edge_index = edge_index.to(h_V.device)
        initial_edge_attr = initial_edge_attr.to(h_V.device)

        # 🚀 迭代式协同进化
        h = h_V_flat.clone()
        coords = coords_flat.clone()
        h_initial = h.clone()

        # 🚀 【逻辑闭环】迭代式协同进化核心循环 - 实现真正的双向信息流
        edge_attr_for_iteration = initial_edge_attr  # 初始化边特征

        for iteration in range(self.num_iterations):
            # 第1步：结构更新 - 使用上一轮迭代的边特征（如果启用坐标更新）
            if self.use_coords_update:
                _, coords_updated, _ = self.egc_layer(
                    h, edge_index, coords, edge_attr=edge_attr_for_iteration
                )
            else:
                coords_updated = coords  # 保持坐标不变

            # 第2步：安全的边特征重计算 - 基于更新后的坐标（如果坐标有更新）
            if self.use_coords_update:
                try:
                    edge_attr_updated = self.recompute_edge_features(coords_updated, edge_index, iteration=iteration)
                    # 验证维度是否匹配
                    if edge_attr_updated.size(0) != edge_index.size(1):
                        print(f"⚠️ 边特征维度不匹配: {edge_attr_updated.size(0)} vs {edge_index.size(1)}, 使用原始边特征")
                        edge_attr_updated = edge_attr_for_iteration
                except Exception as e:
                    print(f"⚠️ 边特征重计算失败: {e}, 使用原始边特征")
                    edge_attr_updated = edge_attr_for_iteration
            else:
                edge_attr_updated = edge_attr_for_iteration  # 保持原始边特征

            # 第3步：特征更新 - 使用最新的边特征
            h_gt = self.gt_layer(h, edge_index, edge_attr_updated)

            # 🚀 【关键改进1】：添加GCN全局信息传播
            if self.use_gcn_fusion:
                adj = self._build_adjacency_matrix(edge_index, h.size(0))
                h_gcn = self.gcn_layer(h, adj)

                # 门控融合GT和GCN的输出
                fusion_input = torch.cat([h_gt, h_gcn], dim=1)
                fusion_gate = torch.sigmoid(self.fusion_gate(fusion_input))
                h_gt = fusion_gate * h_gt + (1 - fusion_gate) * h_gcn

            # 🚀 【关键改进2】：直达初始层的残差连接
            residual_input = torch.cat([h_gt, h_initial], dim=1)
            h_residual = self.initial_residual_gate(residual_input)
            h_gt = (1 - self.initial_residual_weight) * h_gt + self.initial_residual_weight * h_residual

            # 第4步：残差更新
            h = self.norm_h(h + self.residual_weight_h * (h_gt - h))
            if self.use_coords_update:
                coords = self.norm_coords(coords + self.residual_weight_coords * (coords_updated - coords))
            # 如果不更新坐标，coords保持不变

            # 🚀 【关键】为下一轮迭代准备边特征 - 实现双向信息流
            edge_attr_for_iteration = edge_attr_updated

            # 数值稳定性保护
            h = torch.clamp(h, min=-10.0, max=10.0)
            coords = torch.clamp(coords, min=-100.0, max=100.0)

        # 最终融合
        h_final = self.final_fusion(torch.cat([h_initial, h], dim=-1))
        h_final = torch.clamp(h_final, min=-5.0, max=5.0)

        # 重新整形回批量格式
        h_V_out = h_final.view(batch_size, seq_len, hidden_dim)
        coords_out = coords.view(batch_size, seq_len, 3)

        # 应用掩码
        if mask_V is not None:
            mask_expanded = mask_V.unsqueeze(-1)
            h_V_out = h_V_out * mask_expanded
            coords_out = coords_out * mask_expanded

        return h_V_out, coords_out


def convert_mvgnn_to_gte_format(h_V, h_E, E_idx):
    """
    辅助函数：将MVGNN的数据格式转换为GTE_Block需要的格式

    Args:
        h_V: [B, L, D] - 节点特征
        h_E: [B, L, K, D_edge] - 边特征
        E_idx: [B, L, K] - 边索引

    Returns:
        转换后的数据，可以直接输入GTE_Block
    """
    # 这个函数主要是为了兼容性，实际上GTE_Block已经处理了格式转换
    return h_V, h_E, E_idx
