MVGNN-GTE Optimal Training Configuration:
{'node_features': 1041, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Epoch 1/50 - Train Loss: 0.856580, Val Loss: 0.826998, Train AUC: 0.521527, Val AUC: 0.651571, Train F1: 0.270089, Val F1: 0.323346, Train Precision: 0.156288, Val Precision: 0.231599, Train Recall: 0.993514, Val Recall: 0.535475
🎯 Model saved at epoch 1 with validation AUC: 0.651571
Epoch 2/50 - Train Loss: 0.802314, Val Loss: 0.739956, Train AUC: 0.665113, Val AUC: 0.750451, Train F1: 0.339450, Val F1: 0.412303, Train Precision: 0.240016, Val Precision: 0.347099, Train Recall: 0.579540, Val Recall: 0.507670
🎯 Model saved at epoch 2 with validation AUC: 0.750451
Epoch 3/50 - Train Loss: 0.736188, Val Loss: 0.707298, Train AUC: 0.750012, Val AUC: 0.780084, Train F1: 0.424523, Val F1: 0.445833, Train Precision: 0.330294, Val Precision: 0.355132, Train Recall: 0.593979, Val Recall: 0.598754
🎯 Model saved at epoch 3 with validation AUC: 0.780084
Epoch 4/50 - Train Loss: 0.675932, Val Loss: 0.763410, Train AUC: 0.799122, Val AUC: 0.785338, Train F1: 0.473355, Val F1: 0.449358, Train Precision: 0.396351, Val Precision: 0.370867, Train Recall: 0.587494, Val Recall: 0.569990
🎯 Model saved at epoch 4 with validation AUC: 0.785338
Epoch 5/50 - Train Loss: 0.633824, Val Loss: 0.726499, Train AUC: 0.830699, Val AUC: 0.789935, Train F1: 0.511181, Val F1: 0.455474, Train Precision: 0.454988, Val Precision: 0.396797, Train Recall: 0.583211, Val Recall: 0.534516
🎯 Model saved at epoch 5 with validation AUC: 0.789935
Epoch 6/50 - Train Loss: 0.617499, Val Loss: 0.748731, Train AUC: 0.840409, Val AUC: 0.799744, Train F1: 0.522246, Val F1: 0.473388, Train Precision: 0.448603, Val Precision: 0.419224, Train Recall: 0.624816, Val Recall: 0.543624
🎯 Model saved at epoch 6 with validation AUC: 0.799744
Epoch 7/50 - Train Loss: 0.572794, Val Loss: 0.767360, Train AUC: 0.868679, Val AUC: 0.792053, Train F1: 0.562210, Val F1: 0.469271, Train Precision: 0.478052, Val Precision: 0.407709, Train Recall: 0.682330, Val Recall: 0.552733
Epoch 8/50 - Train Loss: 0.522328, Val Loss: 0.796532, Train AUC: 0.890250, Val AUC: 0.786447, Train F1: 0.600033, Val F1: 0.461104, Train Precision: 0.540926, Val Precision: 0.379770, Train Recall: 0.673642, Val Recall: 0.586769
Epoch 9/50 - Train Loss: 0.469731, Val Loss: 0.782834, Train AUC: 0.912963, Val AUC: 0.777099, Train F1: 0.651041, Val F1: 0.437824, Train Precision: 0.597242, Val Precision: 0.365415, Train Recall: 0.715492, Val Recall: 0.546021
Epoch 10/50 - Train Loss: 0.417066, Val Loss: 0.908403, Train AUC: 0.932621, Val AUC: 0.761143, Train F1: 0.687085, Val F1: 0.451021, Train Precision: 0.626664, Val Precision: 0.398968, Train Recall: 0.760401, Val Recall: 0.518696
Epoch 11/50 - Train Loss: 0.398416, Val Loss: 0.898014, Train AUC: 0.940026, Val AUC: 0.757699, Train F1: 0.712510, Val F1: 0.435307, Train Precision: 0.678090, Val Precision: 0.345332, Train Recall: 0.750612, Val Recall: 0.588686
Epoch 12/50 - Train Loss: 0.361545, Val Loss: 0.970976, Train AUC: 0.951352, Val AUC: 0.751966, Train F1: 0.738479, Val F1: 0.448733, Train Precision: 0.686429, Val Precision: 0.370625, Train Recall: 0.799070, Val Recall: 0.568552
Epoch 13/50 - Train Loss: 0.353647, Val Loss: 1.052429, Train AUC: 0.953635, Val AUC: 0.732517, Train F1: 0.738210, Val F1: 0.437646, Train Precision: 0.680644, Val Precision: 0.368680, Train Recall: 0.806412, Val Recall: 0.538351
Epoch 14/50 - Train Loss: 0.302252, Val Loss: 1.021269, Train AUC: 0.962014, Val AUC: 0.743060, Train F1: 0.769726, Val F1: 0.441356, Train Precision: 0.715231, Val Precision: 0.393541, Train Recall: 0.833211, Val Recall: 0.502397
Epoch 15/50 - Train Loss: 0.285961, Val Loss: 0.950254, Train AUC: 0.966816, Val AUC: 0.759699, Train F1: 0.795933, Val F1: 0.466321, Train Precision: 0.735645, Val Precision: 0.371689, Train Recall: 0.866985, Val Recall: 0.625599
Epoch 16/50 - Train Loss: 0.287134, Val Loss: 0.999324, Train AUC: 0.966949, Val AUC: 0.750027, Train F1: 0.795256, Val F1: 0.458246, Train Precision: 0.757475, Val Precision: 0.380998, Train Recall: 0.837004, Val Recall: 0.574784
🛑 Early stopping triggered at epoch 16, best validation AUC: 0.799744

✅ Fold 1 completed with best AUC: 0.799744
============================================================

