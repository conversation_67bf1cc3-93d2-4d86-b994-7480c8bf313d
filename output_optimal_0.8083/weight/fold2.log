MVGNN-GTE Optimal Training Configuration:
{'node_features': 1041, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Epoch 1/50 - Train Loss: 0.857506, Val Loss: 0.792046, Train AUC: 0.533589, Val AUC: 0.666476, Train F1: 0.271315, Val F1: 0.323031, Train Precision: 0.157085, Val Precision: 0.234368, Train Recall: 0.994489, Val Recall: 0.519598
🎯 Model saved at epoch 1 with validation AUC: 0.666476
Epoch 2/50 - Train Loss: 0.795024, Val Loss: 0.743237, Train AUC: 0.660980, Val AUC: 0.767243, Train F1: 0.344417, Val F1: 0.430574, Train Precision: 0.252483, Val Precision: 0.353483, Train Recall: 0.541636, Val Recall: 0.550669
🎯 Model saved at epoch 2 with validation AUC: 0.767243
Epoch 3/50 - Train Loss: 0.744427, Val Loss: 0.682491, Train AUC: 0.741715, Val AUC: 0.790286, Train F1: 0.422702, Val F1: 0.447810, Train Precision: 0.324525, Val Precision: 0.362161, Train Recall: 0.606049, Val Recall: 0.586520
🎯 Model saved at epoch 3 with validation AUC: 0.790286
Epoch 4/50 - Train Loss: 0.701423, Val Loss: 0.712411, Train AUC: 0.777853, Val AUC: 0.786355, Train F1: 0.464378, Val F1: 0.442729, Train Precision: 0.390325, Val Precision: 0.349681, Train Recall: 0.573108, Val Recall: 0.603250
Epoch 5/50 - Train Loss: 0.660349, Val Loss: 0.707004, Train AUC: 0.818148, Val AUC: 0.817085, Train F1: 0.502734, Val F1: 0.491052, Train Precision: 0.418061, Val Precision: 0.423811, Train Recall: 0.630419, Val Recall: 0.583652
🎯 Model saved at epoch 5 with validation AUC: 0.817085
Epoch 6/50 - Train Loss: 0.619302, Val Loss: 0.682890, Train AUC: 0.843163, Val AUC: 0.809101, Train F1: 0.532006, Val F1: 0.471390, Train Precision: 0.448172, Val Precision: 0.397571, Train Recall: 0.654421, Val Recall: 0.578872
Epoch 7/50 - Train Loss: 0.595459, Val Loss: 0.680303, Train AUC: 0.853435, Val AUC: 0.810718, Train F1: 0.544222, Val F1: 0.476361, Train Precision: 0.471274, Val Precision: 0.380871, Train Recall: 0.643889, Val Recall: 0.635755
Epoch 8/50 - Train Loss: 0.570134, Val Loss: 0.735896, Train AUC: 0.873644, Val AUC: 0.805985, Train F1: 0.567898, Val F1: 0.469970, Train Precision: 0.473620, Val Precision: 0.407291, Train Recall: 0.709037, Val Recall: 0.555449
Epoch 9/50 - Train Loss: 0.503544, Val Loss: 0.906366, Train AUC: 0.901940, Val AUC: 0.770848, Train F1: 0.618519, Val F1: 0.445911, Train Precision: 0.534361, Val Precision: 0.350150, Train Recall: 0.734142, Val Recall: 0.613767
Epoch 10/50 - Train Loss: 0.441698, Val Loss: 0.965304, Train AUC: 0.924913, Val AUC: 0.757907, Train F1: 0.667286, Val F1: 0.446676, Train Precision: 0.602866, Val Precision: 0.369940, Train Recall: 0.747122, Val Recall: 0.563576
Epoch 11/50 - Train Loss: 0.415529, Val Loss: 0.920295, Train AUC: 0.935915, Val AUC: 0.760745, Train F1: 0.688720, Val F1: 0.454082, Train Precision: 0.622148, Val Precision: 0.385153, Train Recall: 0.771247, Val Recall: 0.553059
Epoch 12/50 - Train Loss: 0.400556, Val Loss: 1.032175, Train AUC: 0.940831, Val AUC: 0.735267, Train F1: 0.709215, Val F1: 0.453083, Train Precision: 0.648978, Val Precision: 0.364350, Train Recall: 0.781778, Val Recall: 0.598948
Epoch 13/50 - Train Loss: 0.368644, Val Loss: 0.910555, Train AUC: 0.948609, Val AUC: 0.765316, Train F1: 0.729293, Val F1: 0.462510, Train Precision: 0.673089, Val Precision: 0.385523, Train Recall: 0.795738, Val Recall: 0.577916
Epoch 14/50 - Train Loss: 0.379518, Val Loss: 1.011509, Train AUC: 0.946496, Val AUC: 0.742428, Train F1: 0.724359, Val F1: 0.449514, Train Precision: 0.673796, Val Precision: 0.384198, Train Recall: 0.783125, Val Recall: 0.541587
Epoch 15/50 - Train Loss: 0.328138, Val Loss: 1.029532, Train AUC: 0.958138, Val AUC: 0.737862, Train F1: 0.767513, Val F1: 0.444143, Train Precision: 0.734445, Val Precision: 0.373251, Train Recall: 0.803698, Val Recall: 0.548279
🛑 Early stopping triggered at epoch 15, best validation AUC: 0.817085

✅ Fold 2 completed with best AUC: 0.817085
============================================================

