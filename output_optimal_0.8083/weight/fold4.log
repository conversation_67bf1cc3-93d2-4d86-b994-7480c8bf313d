MVGNN-GTE Optimal Training Configuration:
{'node_features': 1041, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Epoch 1/50 - Train Loss: 0.839407, Val Loss: 0.860509, Train AUC: 0.541619, Val AUC: 0.657792, Train F1: 0.262954, Val F1: 0.366613, Train Precision: 0.153302, Val Precision: 0.262398, Train Recall: 0.923499, Val Recall: 0.608147
🎯 Model saved at epoch 1 with validation AUC: 0.657792
Epoch 2/50 - Train Loss: 0.775770, Val Loss: 0.825106, Train AUC: 0.685085, Val AUC: 0.749131, Train F1: 0.353964, Val F1: 0.447981, Train Precision: 0.261779, Val Precision: 0.346113, Train Recall: 0.546364, Val Recall: 0.634821
🎯 Model saved at epoch 2 with validation AUC: 0.749131
Epoch 3/50 - Train Loss: 0.706937, Val Loss: 0.810059, Train AUC: 0.759152, Val AUC: 0.768142, Train F1: 0.425523, Val F1: 0.454563, Train Precision: 0.329755, Val Precision: 0.377443, Train Recall: 0.599683, Val Recall: 0.571290
🎯 Model saved at epoch 3 with validation AUC: 0.768142
Epoch 4/50 - Train Loss: 0.671183, Val Loss: 0.813604, Train AUC: 0.796266, Val AUC: 0.773456, Train F1: 0.470936, Val F1: 0.463078, Train Precision: 0.390293, Val Precision: 0.379214, Train Recall: 0.593582, Val Recall: 0.594568
🎯 Model saved at epoch 4 with validation AUC: 0.773456
Epoch 5/50 - Train Loss: 0.630889, Val Loss: 0.774966, Train AUC: 0.824212, Val AUC: 0.785206, Train F1: 0.503341, Val F1: 0.478159, Train Precision: 0.434825, Val Precision: 0.379891, Train Recall: 0.597487, Val Recall: 0.645005
🎯 Model saved at epoch 5 with validation AUC: 0.785206
Epoch 6/50 - Train Loss: 0.592469, Val Loss: 0.814580, Train AUC: 0.847615, Val AUC: 0.788813, Train F1: 0.523819, Val F1: 0.484071, Train Precision: 0.450739, Val Precision: 0.412427, Train Recall: 0.625183, Val Recall: 0.585839
🎯 Model saved at epoch 6 with validation AUC: 0.788813
Epoch 7/50 - Train Loss: 0.564145, Val Loss: 0.831502, Train AUC: 0.867261, Val AUC: 0.771925, Train F1: 0.557913, Val F1: 0.459779, Train Precision: 0.488846, Val Precision: 0.387375, Train Recall: 0.649707, Val Recall: 0.565470
Epoch 8/50 - Train Loss: 0.501903, Val Loss: 0.851525, Train AUC: 0.896744, Val AUC: 0.776726, Train F1: 0.604263, Val F1: 0.468261, Train Precision: 0.527432, Val Precision: 0.407168, Train Recall: 0.707296, Val Recall: 0.550921
Epoch 9/50 - Train Loss: 0.475670, Val Loss: 1.018753, Train AUC: 0.908938, Val AUC: 0.757162, Train F1: 0.627110, Val F1: 0.462004, Train Precision: 0.570828, Val Precision: 0.378563, Train Recall: 0.695705, Val Recall: 0.592629
Epoch 10/50 - Train Loss: 0.435273, Val Loss: 1.122057, Train AUC: 0.922876, Val AUC: 0.758110, Train F1: 0.664523, Val F1: 0.480482, Train Precision: 0.608983, Val Precision: 0.420116, Train Recall: 0.731210, Val Recall: 0.561106
Epoch 11/50 - Train Loss: 0.401197, Val Loss: 1.030843, Train AUC: 0.937154, Val AUC: 0.758122, Train F1: 0.694927, Val F1: 0.474806, Train Precision: 0.621476, Val Precision: 0.395416, Train Recall: 0.788067, Val Recall: 0.594083
Epoch 12/50 - Train Loss: 0.398488, Val Loss: 1.053531, Train AUC: 0.940117, Val AUC: 0.756077, Train F1: 0.704262, Val F1: 0.470979, Train Precision: 0.655321, Val Precision: 0.394435, Train Recall: 0.761103, Val Recall: 0.584384
Epoch 13/50 - Train Loss: 0.344780, Val Loss: 0.964647, Train AUC: 0.952686, Val AUC: 0.768521, Train F1: 0.741481, Val F1: 0.474445, Train Precision: 0.696664, Val Precision: 0.372647, Train Recall: 0.792460, Val Recall: 0.652764
Epoch 14/50 - Train Loss: 0.326554, Val Loss: 0.990760, Train AUC: 0.957810, Val AUC: 0.766008, Train F1: 0.758420, Val F1: 0.482667, Train Precision: 0.699392, Val Precision: 0.397428, Train Recall: 0.828331, Val Recall: 0.614452
Epoch 15/50 - Train Loss: 0.342239, Val Loss: 1.094172, Train AUC: 0.955389, Val AUC: 0.753902, Train F1: 0.762187, Val F1: 0.481531, Train Precision: 0.715402, Val Precision: 0.395089, Train Recall: 0.815520, Val Recall: 0.616392
Epoch 16/50 - Train Loss: 0.308229, Val Loss: 1.067494, Train AUC: 0.962835, Val AUC: 0.761379, Train F1: 0.773111, Val F1: 0.481206, Train Precision: 0.702051, Val Precision: 0.448971, Train Recall: 0.860176, Val Recall: 0.518429
🛑 Early stopping triggered at epoch 16, best validation AUC: 0.788813

✅ Fold 4 completed with best AUC: 0.788813
============================================================

