MVGNN-GTE Optimal Training Configuration:
{'node_features': 1041, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Epoch 1/50 - Train Loss: 0.858210, Val Loss: 0.790976, Train AUC: 0.545158, Val AUC: 0.653664, Train F1: 0.273530, Val F1: 0.318129, Train Precision: 0.162479, Val Precision: 0.223484, Train Recall: 0.864181, Val Recall: 0.551828
🎯 Model saved at epoch 1 with validation AUC: 0.653664
Epoch 2/50 - Train Loss: 0.788208, Val Loss: 0.727902, Train AUC: 0.682019, Val AUC: 0.764715, Train F1: 0.359032, Val F1: 0.424084, Train Precision: 0.253322, Val Precision: 0.354665, Train Recall: 0.616148, Val Recall: 0.527291
🎯 Model saved at epoch 2 with validation AUC: 0.764715
Epoch 3/50 - Train Loss: 0.714647, Val Loss: 0.778089, Train AUC: 0.761923, Val AUC: 0.786926, Train F1: 0.443233, Val F1: 0.453066, Train Precision: 0.346371, Val Precision: 0.388273, Train Recall: 0.615301, Val Recall: 0.543816
🎯 Model saved at epoch 3 with validation AUC: 0.786926
Epoch 4/50 - Train Loss: 0.688563, Val Loss: 0.658487, Train AUC: 0.792259, Val AUC: 0.807479, Train F1: 0.466548, Val F1: 0.473878, Train Precision: 0.374597, Val Precision: 0.399931, Train Recall: 0.618327, Val Recall: 0.581372
🎯 Model saved at epoch 4 with validation AUC: 0.807479
Epoch 5/50 - Train Loss: 0.655040, Val Loss: 0.653915, Train AUC: 0.818364, Val AUC: 0.807848, Train F1: 0.498047, Val F1: 0.468197, Train Precision: 0.393255, Val Precision: 0.375038, Train Recall: 0.678973, Val Recall: 0.622934
🎯 Model saved at epoch 5 with validation AUC: 0.807848
Epoch 6/50 - Train Loss: 0.623094, Val Loss: 0.734600, Train AUC: 0.840874, Val AUC: 0.798567, Train F1: 0.528524, Val F1: 0.454717, Train Precision: 0.444042, Val Precision: 0.391745, Train Recall: 0.652705, Val Recall: 0.541813
Epoch 7/50 - Train Loss: 0.583291, Val Loss: 0.657136, Train AUC: 0.866996, Val AUC: 0.816874, Train F1: 0.565396, Val F1: 0.491699, Train Precision: 0.494734, Val Precision: 0.427619, Train Recall: 0.659605, Val Recall: 0.578368
🎯 Model saved at epoch 7 with validation AUC: 0.816874
Epoch 8/50 - Train Loss: 0.525586, Val Loss: 0.730809, Train AUC: 0.890298, Val AUC: 0.805320, Train F1: 0.601622, Val F1: 0.473928, Train Precision: 0.532383, Val Precision: 0.408481, Train Recall: 0.691563, Val Recall: 0.564347
Epoch 9/50 - Train Loss: 0.515554, Val Loss: 0.843387, Train AUC: 0.902858, Val AUC: 0.776944, Train F1: 0.624911, Val F1: 0.475957, Train Precision: 0.538065, Val Precision: 0.428457, Train Recall: 0.745188, Val Recall: 0.535303
Epoch 10/50 - Train Loss: 0.473637, Val Loss: 0.759849, Train AUC: 0.913495, Val AUC: 0.789008, Train F1: 0.647235, Val F1: 0.469207, Train Precision: 0.558168, Val Precision: 0.420739, Train Recall: 0.770125, Val Recall: 0.530295
Epoch 11/50 - Train Loss: 0.443145, Val Loss: 0.799077, Train AUC: 0.927152, Val AUC: 0.794077, Train F1: 0.682465, Val F1: 0.479282, Train Precision: 0.619315, Val Precision: 0.417877, Train Recall: 0.759956, Val Recall: 0.561843
Epoch 12/50 - Train Loss: 0.421834, Val Loss: 0.805609, Train AUC: 0.935521, Val AUC: 0.792080, Train F1: 0.689465, Val F1: 0.472300, Train Precision: 0.633296, Val Precision: 0.376338, Train Recall: 0.756567, Val Recall: 0.633951
Epoch 13/50 - Train Loss: 0.371631, Val Loss: 0.766867, Train AUC: 0.947055, Val AUC: 0.800103, Train F1: 0.730203, Val F1: 0.488240, Train Precision: 0.676972, Val Precision: 0.431985, Train Recall: 0.792519, Val Recall: 0.561342
Epoch 14/50 - Train Loss: 0.352619, Val Loss: 0.820632, Train AUC: 0.953164, Val AUC: 0.785160, Train F1: 0.743646, Val F1: 0.486102, Train Precision: 0.711192, Val Precision: 0.449066, Train Recall: 0.779203, Val Recall: 0.529795
Epoch 15/50 - Train Loss: 0.338861, Val Loss: 0.818089, Train AUC: 0.955695, Val AUC: 0.780889, Train F1: 0.758177, Val F1: 0.479233, Train Precision: 0.716223, Val Precision: 0.398539, Train Recall: 0.805350, Val Recall: 0.600901
Epoch 16/50 - Train Loss: 0.334790, Val Loss: 0.847376, Train AUC: 0.957962, Val AUC: 0.777613, Train F1: 0.760599, Val F1: 0.476402, Train Precision: 0.728290, Val Precision: 0.428858, Train Recall: 0.795908, Val Recall: 0.535804
Epoch 17/50 - Train Loss: 0.323396, Val Loss: 0.928473, Train AUC: 0.965037, Val AUC: 0.767488, Train F1: 0.787296, Val F1: 0.481213, Train Precision: 0.750219, Val Precision: 0.419427, Train Recall: 0.828229, Val Recall: 0.564347
🛑 Early stopping triggered at epoch 17, best validation AUC: 0.816874

✅ Fold 3 completed with best AUC: 0.816874
============================================================

