MVGNN-GTE Optimal Training Configuration:
{'node_features': 1041, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}


🔄 训练第 4/5 折
📊 训练样本: 268
📊 验证样本: 67

✅ Fold 4 completed with best AUC: 0.788813
============================================================

