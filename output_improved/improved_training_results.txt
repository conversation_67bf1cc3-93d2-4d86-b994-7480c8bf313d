MVGNN-GTE Improved Training Results Summary
Fusion of Successful MVGNN Design
============================================================
Training Configuration:
{'node_features': 1297, 'use_golden_features': False, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'use_coords_update': False, 'use_gcn_fusion': True, 'use_stable_scheduler': True, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Cross-Validation Results:
Fold 1: AUC = 0.549747
Fold 2: AUC = 0.578105
Fold 3: AUC = 0.790468
Fold 4: AUC = 0.766615
Fold 5: AUC = 0.539900

Final Results:
Mean AUC: 0.644967 ± 0.110041
Best AUC: 0.790468
Training completed at: 2025-07-31 00:16:35
