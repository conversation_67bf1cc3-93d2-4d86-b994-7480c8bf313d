MVGNN-GTE Optimal Training Configuration:
{'node_features': 1297, 'use_golden_features': False, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'use_coords_update': Fals<PERSON>, 'use_gcn_fusion': True, 'use_stable_scheduler': True, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Epoch 1/50 - Train Loss: 0.878616, Val Loss: 0.930966, Train AUC: 0.496908, Val AUC: 0.557500, Train F1: 0.260652, Val F1: 0.308846, Train Precision: 0.149952, Val Precision: 0.187345, Train Recall: 0.995730, Val Recall: 0.878758
🎯 Model saved at epoch 1 with validation AUC: 0.557500
Epoch 2/50 - Train Loss: 0.859271, Val Loss: 0.961702, Train AUC: 0.513038, Val AUC: 0.522598, Train F1: 0.260437, Val F1: 0.304477, Train Precision: 0.149720, Val Precision: 0.179703, Train Recall: 0.999756, Val Recall: 0.996120
Epoch 3/50 - Train Loss: 0.772536, Val Loss: 0.857166, Train AUC: 0.513422, Val AUC: 0.549687, Train F1: 0.260426, Val F1: 0.304438, Train Precision: 0.149707, Val Precision: 0.185030, Train Recall: 1.000000, Val Recall: 0.858390
Epoch 4/50 - Train Loss: 0.776887, Val Loss: 0.838097, Train AUC: 0.494057, Val AUC: 0.546477, Train F1: 0.260426, Val F1: 0.313680, Train Precision: 0.149707, Val Precision: 0.195483, Train Recall: 1.000000, Val Recall: 0.793404
Epoch 5/50 - Train Loss: 0.762208, Val Loss: 0.844926, Train AUC: 0.514352, Val AUC: 0.520796, Train F1: 0.260565, Val F1: 0.302790, Train Precision: 0.149804, Val Precision: 0.178405, Train Recall: 0.999756, Val Recall: 1.000000
Epoch 6/50 - Train Loss: 0.685915, Val Loss: 0.767606, Train AUC: 0.517522, Val AUC: 0.529750, Train F1: 0.260426, Val F1: 0.303175, Train Precision: 0.149707, Val Precision: 0.180141, Train Recall: 1.000000, Val Recall: 0.956353
Epoch 7/50 - Train Loss: 0.608111, Val Loss: 0.665863, Train AUC: 0.526128, Val AUC: 0.559692, Train F1: 0.260517, Val F1: 0.312810, Train Precision: 0.149852, Val Precision: 0.202366, Train Recall: 0.996218, Val Recall: 0.688652
🎯 Model saved at epoch 7 with validation AUC: 0.559692
Epoch 8/50 - Train Loss: 0.602789, Val Loss: 0.656507, Train AUC: 0.570269, Val AUC: 0.627251, Train F1: 0.270229, Val F1: 0.352977, Train Precision: 0.175756, Val Precision: 0.256664, Train Recall: 0.584309, Val Recall: 0.564985
🎯 Model saved at epoch 8 with validation AUC: 0.627251
Epoch 9/50 - Train Loss: 0.597809, Val Loss: 0.657600, Train AUC: 0.597373, Val AUC: 0.629292, Train F1: 0.290492, Val F1: 0.361251, Train Precision: 0.206138, Val Precision: 0.286979, Train Recall: 0.491703, Val Recall: 0.487391
🎯 Model saved at epoch 9 with validation AUC: 0.629292
Epoch 10/50 - Train Loss: 0.592967, Val Loss: 0.631010, Train AUC: 0.616409, Val AUC: 0.658109, Train F1: 0.311879, Val F1: 0.365449, Train Precision: 0.222199, Val Precision: 0.245932, Train Recall: 0.522938, Val Recall: 0.710960
🎯 Model saved at epoch 10 with validation AUC: 0.658109
Epoch 11/50 - Train Loss: 0.575172, Val Loss: 0.638071, Train AUC: 0.658584, Val AUC: 0.677449, Train F1: 0.336804, Val F1: 0.388158, Train Precision: 0.233532, Val Precision: 0.311437, Train Recall: 0.603831, Val Recall: 0.515034
🎯 Model saved at epoch 11 with validation AUC: 0.677449
Epoch 12/50 - Train Loss: 0.572489, Val Loss: 0.657647, Train AUC: 0.680993, Val AUC: 0.659627, Train F1: 0.352742, Val F1: 0.360955, Train Precision: 0.252488, Val Precision: 0.249444, Train Recall: 0.585041, Val Recall: 0.652764
Epoch 13/50 - Train Loss: 0.564181, Val Loss: 0.690439, Train AUC: 0.686387, Val AUC: 0.672297, Train F1: 0.363890, Val F1: 0.385874, Train Precision: 0.266448, Val Precision: 0.281759, Train Recall: 0.573694, Val Recall: 0.612027
Epoch 14/50 - Train Loss: 0.544452, Val Loss: 0.632822, Train AUC: 0.717638, Val AUC: 0.699585, Train F1: 0.392223, Val F1: 0.399834, Train Precision: 0.298698, Val Precision: 0.279868, Train Recall: 0.571010, Val Recall: 0.699806
🎯 Model saved at epoch 14 with validation AUC: 0.699585
Epoch 15/50 - Train Loss: 0.531137, Val Loss: 0.679722, Train AUC: 0.740489, Val AUC: 0.695165, Train F1: 0.409486, Val F1: 0.401658, Train Precision: 0.300958, Val Precision: 0.299145, Train Recall: 0.640434, Val Recall: 0.611057
Epoch 16/50 - Train Loss: 0.512641, Val Loss: 0.669266, Train AUC: 0.761255, Val AUC: 0.723383, Train F1: 0.436308, Val F1: 0.408825, Train Precision: 0.347055, Val Precision: 0.317112, Train Recall: 0.587360, Val Recall: 0.575170
🎯 Model saved at epoch 16 with validation AUC: 0.723383
Epoch 17/50 - Train Loss: 0.510718, Val Loss: 0.619708, Train AUC: 0.773988, Val AUC: 0.729792, Train F1: 0.453391, Val F1: 0.426274, Train Precision: 0.358608, Val Precision: 0.308865, Train Recall: 0.616276, Val Recall: 0.687682
🎯 Model saved at epoch 17 with validation AUC: 0.729792
Epoch 18/50 - Train Loss: 0.500333, Val Loss: 0.644629, Train AUC: 0.780780, Val AUC: 0.738664, Train F1: 0.455927, Val F1: 0.423639, Train Precision: 0.353917, Val Precision: 0.307373, Train Recall: 0.640556, Val Recall: 0.681377
🎯 Model saved at epoch 18 with validation AUC: 0.738664
Epoch 19/50 - Train Loss: 0.492561, Val Loss: 0.720601, Train AUC: 0.796572, Val AUC: 0.721286, Train F1: 0.475955, Val F1: 0.422150, Train Precision: 0.395400, Val Precision: 0.335429, Train Recall: 0.597731, Val Recall: 0.569350
Epoch 20/50 - Train Loss: 0.477445, Val Loss: 0.651775, Train AUC: 0.805551, Val AUC: 0.745842, Train F1: 0.496585, Val F1: 0.445918, Train Precision: 0.406192, Val Precision: 0.343373, Train Recall: 0.638726, Val Recall: 0.635790
🎯 Model saved at epoch 20 with validation AUC: 0.745842
Epoch 21/50 - Train Loss: 0.468991, Val Loss: 0.661835, Train AUC: 0.822385, Val AUC: 0.765380, Train F1: 0.512097, Val F1: 0.453738, Train Precision: 0.426014, Val Precision: 0.366846, Train Recall: 0.641776, Val Recall: 0.594568
🎯 Model saved at epoch 21 with validation AUC: 0.765380
Epoch 22/50 - Train Loss: 0.446331, Val Loss: 0.696818, Train AUC: 0.841946, Val AUC: 0.738673, Train F1: 0.530660, Val F1: 0.454645, Train Precision: 0.452767, Val Precision: 0.364944, Train Recall: 0.640922, Val Recall: 0.602813
Epoch 23/50 - Train Loss: 0.450868, Val Loss: 0.716256, Train AUC: 0.835631, Val AUC: 0.736709, Train F1: 0.524474, Val F1: 0.463566, Train Precision: 0.446869, Val Precision: 0.386056, Train Recall: 0.634700, Val Recall: 0.580019
Epoch 24/50 - Train Loss: 0.429663, Val Loss: 0.711627, Train AUC: 0.852507, Val AUC: 0.753734, Train F1: 0.546366, Val F1: 0.471014, Train Precision: 0.460406, Val Precision: 0.375940, Train Recall: 0.671791, Val Recall: 0.630456
Epoch 25/50 - Train Loss: 0.419940, Val Loss: 0.648084, Train AUC: 0.862433, Val AUC: 0.762810, Train F1: 0.556797, Val F1: 0.463029, Train Precision: 0.468007, Val Precision: 0.390854, Train Recall: 0.687164, Val Recall: 0.567895
Epoch 26/50 - Train Loss: 0.417737, Val Loss: 0.665773, Train AUC: 0.861368, Val AUC: 0.760181, Train F1: 0.559026, Val F1: 0.465680, Train Precision: 0.468843, Val Precision: 0.385792, Train Recall: 0.692167, Val Recall: 0.587294
Epoch 27/50 - Train Loss: 0.405793, Val Loss: 0.645285, Train AUC: 0.876695, Val AUC: 0.762821, Train F1: 0.574854, Val F1: 0.469874, Train Precision: 0.485340, Val Precision: 0.424765, Train Recall: 0.704856, Val Recall: 0.525703
Epoch 28/50 - Train Loss: 0.405954, Val Loss: 0.660173, Train AUC: 0.876088, Val AUC: 0.766615, Train F1: 0.581082, Val F1: 0.477744, Train Precision: 0.506765, Val Precision: 0.408543, Train Recall: 0.680942, Val Recall: 0.575170
🎯 Model saved at epoch 28 with validation AUC: 0.766615
Epoch 29/50 - Train Loss: 0.380105, Val Loss: 0.685376, Train AUC: 0.891054, Val AUC: 0.756046, Train F1: 0.600997, Val F1: 0.472059, Train Precision: 0.511524, Val Precision: 0.395288, Train Recall: 0.728404, Val Recall: 0.585839
Epoch 30/50 - Train Loss: 0.388549, Val Loss: 0.694936, Train AUC: 0.886350, Val AUC: 0.757470, Train F1: 0.596714, Val F1: 0.470539, Train Precision: 0.521086, Val Precision: 0.415613, Train Recall: 0.698023, Val Recall: 0.542192
Epoch 31/50 - Train Loss: 0.391352, Val Loss: 0.687217, Train AUC: 0.881833, Val AUC: 0.755444, Train F1: 0.585052, Val F1: 0.473542, Train Precision: 0.495141, Val Precision: 0.393453, Train Recall: 0.714861, Val Recall: 0.594568
Epoch 32/50 - Train Loss: 0.372372, Val Loss: 0.701490, Train AUC: 0.896922, Val AUC: 0.750188, Train F1: 0.611374, Val F1: 0.476449, Train Precision: 0.529066, Val Precision: 0.396836, Train Recall: 0.724012, Val Recall: 0.596023
Epoch 33/50 - Train Loss: 0.379981, Val Loss: 0.709873, Train AUC: 0.889403, Val AUC: 0.745238, Train F1: 0.598564, Val F1: 0.476133, Train Precision: 0.519136, Val Precision: 0.405951, Train Recall: 0.706686, Val Recall: 0.575655
Epoch 34/50 - Train Loss: 0.375065, Val Loss: 0.712817, Train AUC: 0.899864, Val AUC: 0.746382, Train F1: 0.616933, Val F1: 0.476508, Train Precision: 0.547336, Val Precision: 0.385167, Train Recall: 0.706808, Val Recall: 0.624636
Epoch 35/50 - Train Loss: 0.358760, Val Loss: 0.718713, Train AUC: 0.905287, Val AUC: 0.748480, Train F1: 0.624070, Val F1: 0.476937, Train Precision: 0.541114, Val Precision: 0.387792, Train Recall: 0.737067, Val Recall: 0.619302
Epoch 36/50 - Train Loss: 0.362649, Val Loss: 0.720380, Train AUC: 0.903315, Val AUC: 0.744247, Train F1: 0.628127, Val F1: 0.471431, Train Precision: 0.554383, Val Precision: 0.385683, Train Recall: 0.724500, Val Recall: 0.606208
Epoch 37/50 - Train Loss: 0.370897, Val Loss: 0.730961, Train AUC: 0.898175, Val AUC: 0.742797, Train F1: 0.614161, Val F1: 0.472265, Train Precision: 0.540423, Val Precision: 0.391693, Train Recall: 0.711201, Val Recall: 0.594568
Epoch 38/50 - Train Loss: 0.352456, Val Loss: 0.748230, Train AUC: 0.909907, Val AUC: 0.737784, Train F1: 0.637053, Val F1: 0.472077, Train Precision: 0.566357, Val Precision: 0.383082, Train Recall: 0.727916, Val Recall: 0.614937
🛑 Early stopping triggered at epoch 38, best validation AUC: 0.766615

✅ Fold 4 completed with best AUC: 0.766615
============================================================

