MVGNN-GTE Optimal Training Configuration:
{'node_features': 1297, 'use_golden_features': False, 'edge_features': 16, 'hidden_dim': 96, 'num_encoder_layers': 4, 'k_neighbors': 30, 'batch_size': 4, 'epochs': 50, 'patience': 10, 'learning_rate': 0.0002, 'dropout': 0.3, 'use_unet_gt': True, 'pooling_ratio': 0.3, 'use_coords_update': Fals<PERSON>, 'use_gcn_fusion': True, 'use_stable_scheduler': True, 'num_iterations': 1, 'use_global_node': True, 'focal_alpha': 0.75, 'focal_gamma': 3.0, 'class_weight_ratio': 5.4}

Epoch 1/50 - Train Loss: 0.862535, Val Loss: 0.839260, Train AUC: 0.519876, Val AUC: 0.549747, Train F1: 0.269824, Val F1: 0.270525, Train Precision: 0.156059, Val Precision: 0.158551, Train Recall: 0.995595, Val Recall: 0.920901
🎯 Model saved at epoch 1 with validation AUC: 0.549747
Epoch 2/50 - Train Loss: 0.850120, Val Loss: 0.842331, Train AUC: 0.518068, Val AUC: 0.517541, Train F1: 0.269839, Val F1: 0.266533, Train Precision: 0.155965, Val Precision: 0.156655, Train Recall: 0.999878, Val Recall: 0.892617
Epoch 3/50 - Train Loss: 0.851759, Val Loss: 0.850819, Train AUC: 0.512955, Val AUC: 0.495330, Train F1: 0.269770, Val F1: 0.261289, Train Precision: 0.155916, Val Precision: 0.150277, Train Recall: 1.000000, Val Recall: 1.000000
Epoch 4/50 - Train Loss: 0.769665, Val Loss: 0.760170, Train AUC: 0.506958, Val AUC: 0.477731, Train F1: 0.269779, Val F1: 0.261109, Train Precision: 0.155921, Val Precision: 0.150158, Train Recall: 1.000000, Val Recall: 1.000000
Epoch 5/50 - Train Loss: 0.678369, Val Loss: 0.668959, Train AUC: 0.510333, Val AUC: 0.517172, Train F1: 0.269775, Val F1: 0.261882, Train Precision: 0.155925, Val Precision: 0.151229, Train Recall: 0.999755, Val Recall: 0.976031
Epoch 6/50 - Train Loss: 0.600607, Val Loss: 0.597004, Train AUC: 0.526811, Val AUC: 0.524107, Train F1: 0.269985, Val F1: 0.263982, Train Precision: 0.156301, Val Precision: 0.155382, Train Recall: 0.990210, Val Recall: 0.876798
Epoch 7/50 - Train Loss: 0.602862, Val Loss: 0.596426, Train AUC: 0.524317, Val AUC: 0.463259, Train F1: 0.269819, Val F1: 0.261174, Train Precision: 0.155948, Val Precision: 0.150202, Train Recall: 1.000000, Val Recall: 1.000000
Epoch 8/50 - Train Loss: 0.532099, Val Loss: 0.532208, Train AUC: 0.535481, Val AUC: 0.534809, Train F1: 0.269979, Val F1: 0.262277, Train Precision: 0.156136, Val Precision: 0.150964, Train Recall: 0.996696, Val Recall: 0.998562
Epoch 9/50 - Train Loss: 0.470453, Val Loss: 0.468751, Train AUC: 0.520813, Val AUC: 0.481925, Train F1: 0.269792, Val F1: 0.261109, Train Precision: 0.155930, Val Precision: 0.150158, Train Recall: 1.000000, Val Recall: 1.000000
Epoch 10/50 - Train Loss: 0.417100, Val Loss: 0.416451, Train AUC: 0.527067, Val AUC: 0.521058, Train F1: 0.269792, Val F1: 0.263645, Train Precision: 0.155930, Val Precision: 0.152073, Train Recall: 1.000000, Val Recall: 0.989933
Epoch 11/50 - Train Loss: 0.372367, Val Loss: 0.370841, Train AUC: 0.538388, Val AUC: 0.542432, Train F1: 0.270896, Val F1: 0.262575, Train Precision: 0.157535, Val Precision: 0.155579, Train Recall: 0.966104, Val Recall: 0.840844
🛑 Early stopping triggered at epoch 11, best validation AUC: 0.549747

✅ Fold 1 completed with best AUC: 0.549747
============================================================

