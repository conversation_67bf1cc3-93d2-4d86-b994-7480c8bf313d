#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 T5特征切换到ESM2特征的便捷脚本

使用方法:
1. 安装ESM: pip install fair-esm
2. 运行此脚本: python switch_to_esm2.py
3. 重新训练模型

特征对比:
- T5: 1024维 → 总特征维度: 1038 (1024 + 14 DSSP)
- ESM2: 1280维 → 总特征维度: 1294 (1280 + 14 DSSP)
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_esm_installation():
    """检查ESM是否已安装"""
    try:
        import esm
        print("✅ ESM已安装")
        return True
    except ImportError:
        print("❌ ESM未安装")
        return False

def install_esm():
    """安装ESM"""
    print("🔄 正在安装ESM...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "fair-esm"])
        print("✅ ESM安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ ESM安装失败")
        return False

def update_pad_feature_config():
    """更新pad_feature.py配置为使用ESM2"""
    pad_feature_path = "process_feature/pad_feature.py"
    
    if not os.path.exists(pad_feature_path):
        print(f"❌ 文件不存在: {pad_feature_path}")
        return False
    
    # 读取文件
    with open(pad_feature_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换配置
    content = content.replace("USE_ESM2 = True", "USE_ESM2 = True")
    content = content.replace("USE_ESM2 = False", "USE_ESM2 = True")
    
    # 写回文件
    with open(pad_feature_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已更新pad_feature.py配置为使用ESM2")
    return True

def extract_esm2_features():
    """提取ESM2特征"""
    print("🔄 开始提取ESM2特征...")
    
    # 检查数据集文件
    train_fasta = "datasets/PRO_Train_335.fa"
    test_fasta = "datasets/PRO_Test_60.fa"
    
    if not os.path.exists(train_fasta):
        print(f"❌ 训练集文件不存在: {train_fasta}")
        return False
    
    if not os.path.exists(test_fasta):
        print(f"❌ 测试集文件不存在: {test_fasta}")
        return False
    
    # 运行特征提取
    try:
        os.chdir("process_feature")
        subprocess.check_call([sys.executable, "process_feature.py"])
        os.chdir("..")
        print("✅ ESM2特征提取完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 特征提取失败: {e}")
        os.chdir("..")
        return False

def backup_t5_features():
    """备份T5特征"""
    t5_dirs = ["T5raw", "T5norm"]
    backup_dir = "backup_T5_features"
    
    if os.path.exists(backup_dir):
        print(f"⚠️ 备份目录已存在: {backup_dir}")
        return True
    
    os.makedirs(backup_dir, exist_ok=True)
    
    for t5_dir in t5_dirs:
        if os.path.exists(t5_dir):
            shutil.move(t5_dir, os.path.join(backup_dir, t5_dir))
            print(f"✅ 已备份 {t5_dir} 到 {backup_dir}")
    
    return True

def show_feature_comparison():
    """显示特征对比信息"""
    print("\n" + "="*60)
    print("📊 T5 vs ESM2 特征对比")
    print("="*60)
    print("T5特征:")
    print("  - 语言模型维度: 1024")
    print("  - DSSP特征维度: 14")
    print("  - 总特征维度: 1038")
    print("  - 模型大小: ~3GB")
    print()
    print("ESM2特征:")
    print("  - 语言模型维度: 1280")
    print("  - DSSP特征维度: 14")
    print("  - 总特征维度: 1294")
    print("  - 模型大小: ~2.5GB")
    print()
    print("优势:")
    print("  - ESM2是专门为蛋白质设计的语言模型")
    print("  - 在蛋白质相关任务上通常表现更好")
    print("  - 训练数据更专业（UniRef50）")
    print("="*60)

def main():
    """主函数"""
    print("🚀 T5特征切换到ESM2特征")
    print("="*50)
    
    # 显示特征对比
    show_feature_comparison()
    
    # 确认操作
    response = input("\n是否继续切换到ESM2特征？(y/N): ").strip().lower()
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    # 1. 检查ESM安装
    if not check_esm_installation():
        if not install_esm():
            print("❌ 无法安装ESM，请手动安装: pip install fair-esm")
            return
    
    # 2. 备份T5特征
    print("\n🔄 备份T5特征...")
    backup_t5_features()
    
    # 3. 更新配置
    print("\n🔄 更新配置文件...")
    update_pad_feature_config()
    
    # 4. 提取ESM2特征
    print("\n🔄 提取ESM2特征...")
    if not extract_esm2_features():
        print("❌ ESM2特征提取失败")
        return
    
    print("\n" + "="*50)
    print("✅ 成功切换到ESM2特征！")
    print("="*50)
    print("📝 接下来的步骤:")
    print("1. 检查特征文件是否正确生成在 ESM2raw/ 和 ESM2norm/ 目录")
    print("2. 运行训练脚本: python train_optimal.py")
    print("3. 模型会自动检测并使用ESM2特征（1294维）")
    print()
    print("💡 如需切换回T5特征:")
    print("1. 修改 process_feature/pad_feature.py 中的 USE_ESM2 = False")
    print("2. 从 backup_T5_features/ 恢复T5特征目录")
    print("="*50)

if __name__ == "__main__":
    main()
