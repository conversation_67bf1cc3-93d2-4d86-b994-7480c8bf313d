#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学习曲线分析脚本 - 诊断模型性能瓶颈
根据您提出的三步诊断方案进行系统性分析
"""

import os
import re
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path

def parse_log_file(log_path):
    """解析训练日志文件，提取训练和验证指标"""
    data = {
        'epoch': [],
        'train_loss': [],
        'val_loss': [],
        'train_auc': [],
        'val_auc': [],
        'train_f1': [],
        'val_f1': []
    }
    
    with open(log_path, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        if 'Epoch' in line and 'Train Loss' in line:
            # 使用正则表达式提取数值
            epoch_match = re.search(r'Epoch (\d+)/\d+', line)
            train_loss_match = re.search(r'Train Loss: ([\d.]+)', line)
            val_loss_match = re.search(r'Val Loss: ([\d.]+)', line)
            train_auc_match = re.search(r'Train AUC: ([\d.]+)', line)
            val_auc_match = re.search(r'Val AUC: ([\d.]+)', line)
            train_f1_match = re.search(r'Train F1: ([\d.]+)', line)
            val_f1_match = re.search(r'Val F1: ([\d.]+)', line)
            
            if all([epoch_match, train_loss_match, val_loss_match, 
                   train_auc_match, val_auc_match, train_f1_match, val_f1_match]):
                data['epoch'].append(int(epoch_match.group(1)))
                data['train_loss'].append(float(train_loss_match.group(1)))
                data['val_loss'].append(float(val_loss_match.group(1)))
                data['train_auc'].append(float(train_auc_match.group(1)))
                data['val_auc'].append(float(val_auc_match.group(1)))
                data['train_f1'].append(float(train_f1_match.group(1)))
                data['val_f1'].append(float(val_f1_match.group(1)))
    
    return pd.DataFrame(data)

def diagnose_training_pattern(df):
    """根据学习曲线诊断训练模式"""
    if len(df) < 5:
        return "数据不足，无法诊断"
    
    # 计算最后几个epoch的趋势
    last_5_epochs = df.tail(5)
    
    # 计算AUC差距
    final_train_auc = df['train_auc'].iloc[-1]
    final_val_auc = df['val_auc'].iloc[-1]
    auc_gap = final_train_auc - final_val_auc
    
    # 计算验证损失趋势
    val_loss_trend = np.polyfit(last_5_epochs['epoch'], last_5_epochs['val_loss'], 1)[0]
    
    # 计算验证AUC的稳定性（标准差）
    val_auc_std = last_5_epochs['val_auc'].std()
    
    print(f"🔍 诊断指标:")
    print(f"  - 最终训练AUC: {final_train_auc:.4f}")
    print(f"  - 最终验证AUC: {final_val_auc:.4f}")
    print(f"  - AUC差距: {auc_gap:.4f}")
    print(f"  - 验证损失趋势: {val_loss_trend:.6f} (负数=下降，正数=上升)")
    print(f"  - 验证AUC稳定性: {val_auc_std:.4f} (越小越稳定)")
    
    # 诊断逻辑
    if auc_gap > 0.15:  # 训练AUC比验证AUC高15%以上
        if val_loss_trend > 0:  # 验证损失在上升
            return "🚨 严重过拟合"
        else:
            return "⚠️ 轻度过拟合"
    elif final_val_auc < 0.75 and final_train_auc < 0.80:  # 两者都较低
        return "📉 欠拟合/模型能力不足"
    elif val_auc_std > 0.02:  # 验证AUC波动较大
        return "📊 训练不稳定"
    else:
        return "✅ 训练相对正常"

def plot_learning_curves(df, fold_name, save_path):
    """绘制学习曲线"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 损失曲线
    ax1.plot(df['epoch'], df['train_loss'], 'b-', label='Train Loss', linewidth=2)
    ax1.plot(df['epoch'], df['val_loss'], 'r-', label='Validation Loss', linewidth=2)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title(f'{fold_name} - Loss Curves')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # AUC曲线
    ax2.plot(df['epoch'], df['train_auc'], 'b-', label='Train AUC', linewidth=2)
    ax2.plot(df['epoch'], df['val_auc'], 'r-', label='Validation AUC', linewidth=2)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('AUC')
    ax2.set_title(f'{fold_name} - AUC Curves')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # F1曲线
    ax3.plot(df['epoch'], df['train_f1'], 'b-', label='Train F1', linewidth=2)
    ax3.plot(df['epoch'], df['val_f1'], 'r-', label='Validation F1', linewidth=2)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('F1 Score')
    ax3.set_title(f'{fold_name} - F1 Curves')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 过拟合指标 (Train AUC - Val AUC)
    overfitting_gap = np.array(df['train_auc']) - np.array(df['val_auc'])
    ax4.plot(df['epoch'], overfitting_gap, 'g-', linewidth=2)
    ax4.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax4.axhline(y=0.1, color='orange', linestyle='--', alpha=0.5, label='轻度过拟合阈值')
    ax4.axhline(y=0.15, color='red', linestyle='--', alpha=0.5, label='严重过拟合阈值')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Train AUC - Val AUC')
    ax4.set_title(f'{fold_name} - Overfitting Gap')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数：分析所有fold的学习曲线"""
    log_dir = Path("output_final_best_config/weight")
    output_dir = Path("learning_curve_analysis")
    output_dir.mkdir(exist_ok=True)
    
    print("🔍 MVGNN-PPIS 学习曲线诊断报告")
    print("=" * 60)
    
    all_diagnoses = []
    all_final_aucs = []
    
    # 分析每个fold
    for fold_num in range(1, 6):
        log_file = log_dir / f"fold{fold_num}.log"
        if not log_file.exists():
            print(f"⚠️ 找不到 {log_file}")
            continue
        
        print(f"\n📊 分析 Fold {fold_num}:")
        print("-" * 30)
        
        # 解析日志
        df = parse_log_file(log_file)
        if df.empty:
            print(f"❌ Fold {fold_num} 日志解析失败")
            continue
        
        # 诊断
        diagnosis = diagnose_training_pattern(df)
        all_diagnoses.append(diagnosis)
        all_final_aucs.append(df['val_auc'].iloc[-1])
        
        print(f"🎯 诊断结果: {diagnosis}")
        
        # 绘制学习曲线
        plot_path = output_dir / f"fold{fold_num}_learning_curves.png"
        plot_learning_curves(df, f"Fold {fold_num}", plot_path)
        print(f"📈 学习曲线已保存: {plot_path}")

    # 总体分析
    print(f"\n🎯 总体诊断结果:")
    print("=" * 60)

    from collections import Counter
    diagnosis_counts = Counter(all_diagnoses)

    for diagnosis, count in diagnosis_counts.items():
        print(f"  {diagnosis}: {count}/5 folds")

    print(f"\n📊 性能统计:")
    print(f"  - 平均验证AUC: {np.mean(all_final_aucs):.4f}")
    print(f"  - AUC标准差: {np.std(all_final_aucs):.4f}")
    print(f"  - 最佳AUC: {np.max(all_final_aucs):.4f}")
    print(f"  - 最差AUC: {np.min(all_final_aucs):.4f}")

    # 生成建议
    print(f"\n💡 改进建议:")
    print("=" * 60)

    most_common_issue = diagnosis_counts.most_common(1)[0][0]

    if "过拟合" in most_common_issue:
        print("🔧 检测到过拟合问题，建议:")
        print("  1. 增加dropout: 从0.3提升到0.4-0.5")
        print("  2. 增加权重衰减: weight_decay从1e-5增加到1e-4")
        print("  3. 简化模型: 设置use_unet_gt=False")
        print("  4. 减少模型容量: hidden_dim从96减少到64")
        print("  5. 早停: 减少patience从8到5")

    elif "欠拟合" in most_common_issue:
        print("🔧 检测到欠拟合问题，建议:")
        print("  1. 增加模型容量: hidden_dim从96增加到128-256")
        print("  2. 增加模型深度: num_encoder_layers从4增加到5-6")
        print("  3. 分模块处理特征: 为ESM2、DSSP、几何特征创建独立处理器")
        print("  4. 降低dropout: 从0.3减少到0.2")
        print("  5. 增加训练轮数: epochs从30增加到50")

    elif "不稳定" in most_common_issue:
        print("🔧 检测到训练不稳定问题，建议:")
        print("  1. 降低学习率: 从0.001降低到1e-4或2e-4")
        print("  2. 使用梯度累积: 模拟更大的batch_size")
        print("  3. 使用更稳定的调度器: ReduceLROnPlateau")
        print("  4. 增加梯度裁剪: max_norm=0.5")

    else:
        print("✅ 训练相对正常，但仍有优化空间:")
        print("  1. 切换到图数据格式: use_graph_format=True")
        print("  2. 尝试不同的特征融合策略")
        print("  3. 调整损失函数权重")
        print("  4. 使用更先进的数据增强")

if __name__ == "__main__":
    main()
